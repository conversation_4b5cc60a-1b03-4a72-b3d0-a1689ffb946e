import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Card,
  CardBody,
  Button,
  Navbar,
  Navbar<PERSON><PERSON>,
  NavbarContent,
  Navbar<PERSON><PERSON>,
  Di<PERSON><PERSON>,
  <PERSON>,
  Spinner
} from "@heroui/react";
import { QuestionMarkCircleIcon } from "@heroicons/react/24/outline";
import { BenchmarkPanel } from "./components/BenchmarkPanel";
import { ChartArea } from "./components/ChartArea";
import { UnitEconomicsPanel } from "./components/UnitEconomicsPanel";
import { MonthlyInputTable } from "./components/MonthlyInputTable";
import { HelpOverlay } from "./components/HelpOverlay";

function App() {
  const [showHelp, setShowHelp] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Show help overlay for first-time users
  useEffect(() => {
    const hasSeenTour = localStorage.getItem('planna-tour-completed');
    if (!hasSeenTour) {
      setShowHelp(true);
    }

    // Simulate loading time for smooth entrance
    setTimeout(() => setIsLoading(false), 1000);
  }, []);

  const handleHelpClose = () => {
    setShowHelp(false);
    localStorage.setItem('planna-tour-completed', 'true');
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-slate-100">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mb-4 mx-auto">
            <span className="text-white font-bold text-2xl">P</span>
          </div>
          <h1 className="text-2xl font-bold text-slate-900 mb-2">Planna</h1>
          <p className="text-slate-600 mb-4">Loading your growth planning tool...</p>
          <Spinner color="primary" size="md" />
        </div>
      </div>
    );
  }
  return (
    <motion.div
      className="h-screen flex flex-col bg-gradient-to-br from-slate-50 to-slate-100"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Modern Header with HeroUI Navbar */}
      <motion.div variants={itemVariants}>
        <Navbar
          isBordered
          className="bg-white/80 backdrop-blur-md border-b border-slate-200/50"
          height="60px"
        >
        <NavbarBrand>
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">P</span>
            </div>
            <div>
              <h1 className="text-xl font-bold text-slate-900">Planna</h1>
              <p className="text-xs text-slate-500 -mt-1">Growth Planning Tool</p>
            </div>
          </div>
        </NavbarBrand>

        <NavbarContent justify="end">
          <NavbarItem>
            <Chip size="sm" variant="flat" color="success">
              Auto-saved
            </Chip>
          </NavbarItem>
          <NavbarItem>
            <Button
              variant="light"
              size="sm"
              onClick={() => setShowHelp(true)}
              startContent={<QuestionMarkCircleIcon className="w-4 h-4" />}
              className="text-slate-600 hover:text-slate-900"
            >
              Help
            </Button>
          </NavbarItem>
          <NavbarItem>
            <Button
              variant="light"
              size="sm"
              className="text-slate-600 hover:text-slate-900"
            >
              Export
            </Button>
          </NavbarItem>
          <NavbarItem>
            <Button
              color="primary"
              size="sm"
              className="bg-gradient-to-r from-blue-500 to-purple-600"
            >
              Save Scenario
            </Button>
          </NavbarItem>
        </NavbarContent>
        </Navbar>
      </motion.div>

      {/* Benchmark Variables Panel */}
      <motion.div variants={itemVariants}>
        <Card className="m-4 mb-2 shadow-sm border-0">
          <CardBody className="p-0">
            <BenchmarkPanel />
          </CardBody>
        </Card>
      </motion.div>

      {/* Main Content Area - Chart + Unit Economics */}
      <motion.div
        className="flex flex-1 min-h-0 gap-4 mx-4 mb-2"
        variants={itemVariants}
      >
        {/* Chart Area - Takes up most space */}
        <motion.div
          className="flex-1"
          whileHover={{ scale: 1.002 }}
          transition={{ duration: 0.2 }}
        >
          <Card className="h-full shadow-sm border-0">
            <CardBody className="p-0 h-full">
              <ChartArea />
            </CardBody>
          </Card>
        </motion.div>

        {/* Unit Economics Panel - Narrower sidebar */}
        <motion.div
          className="w-80"
          whileHover={{ scale: 1.002 }}
          transition={{ duration: 0.2 }}
        >
          <Card className="h-full shadow-sm border-0">
            <CardBody className="p-0 h-full">
              <UnitEconomicsPanel />
            </CardBody>
          </Card>
        </motion.div>
      </motion.div>

      {/* Monthly Input Table - More compact bottom section */}
      <motion.div variants={itemVariants}>
        <Card className="mx-4 mb-4 shadow-sm border-0 h-64">
          <CardBody className="p-0 h-full">
            <MonthlyInputTable />
          </CardBody>
        </Card>
      </motion.div>

      {/* Help Overlay */}
      <AnimatePresence>
        {showHelp && (
          <HelpOverlay isOpen={showHelp} onClose={handleHelpClose} />
        )}
      </AnimatePresence>
    </motion.div>
  );
}

export default App;
