import React from "react";
import { BenchmarkPanel } from "./components/BenchmarkPanel";
import { ChartArea } from "./components/ChartArea";
import { UnitEconomicsPanel } from "./components/UnitEconomicsPanel";
import { MonthlyInputTable } from "./components/MonthlyInputTable";

function App() {
  return (
    <div className="h-screen flex flex-col bg-slate-50">
      {/* Header - Much more compact */}
      <div className="bg-white border-b border-slate-200 px-4 py-2 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h1 className="text-lg font-bold text-slate-900">Planna</h1>
            <span className="text-xs text-slate-500">Growth Planning Tool</span>
          </div>
          <div className="flex items-center gap-2">
            <button className="px-2 py-1 text-xs font-medium text-slate-600 hover:text-slate-900 transition-colors">
              Export
            </button>
            <button className="px-2 py-1 bg-blue-600 text-white text-xs font-medium rounded hover:bg-blue-700 transition-colors">
              Save
            </button>
          </div>
        </div>
      </div>

      {/* Benchmark Variables Panel - Much more compact */}
      <div className="bg-white border-b border-slate-200 flex-shrink-0">
        <BenchmarkPanel />
      </div>

      {/* Main Content Area - Chart + Unit Economics */}
      <div className="flex flex-1 min-h-0">
        {/* Chart Area - Takes up most space */}
        <div className="flex-1 bg-white border-r border-slate-200">
          <ChartArea />
        </div>

        {/* Unit Economics Panel - Narrower sidebar */}
        <div className="w-64 bg-slate-50 border-r border-slate-200">
          <UnitEconomicsPanel />
        </div>
      </div>

      {/* Monthly Input Table - More compact bottom section */}
      <div className="bg-white border-t border-slate-200 flex-shrink-0 h-48">
        <MonthlyInputTable />
      </div>
    </div>
  );
}

export default App;
