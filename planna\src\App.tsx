import React from "react";

function App() {
  console.log("App component rendering");

  return (
    <div style={{
      height: "100vh",
      backgroundColor: "#f0f0f0",
      padding: "20px",
      fontFamily: "Arial, sans-serif"
    }}>
      <h1 style={{
        color: "#333",
        fontSize: "24px",
        marginBottom: "10px"
      }}>
        Planna Desktop App - Basic Test
      </h1>
      <p style={{ color: "#666", fontSize: "16px" }}>
        If you can see this text, <PERSON><PERSON> is working in the Tauri environment!
      </p>
      <div style={{
        marginTop: "20px",
        padding: "15px",
        backgroundColor: "#e0e0e0",
        borderRadius: "5px"
      }}>
        <h2>Next Steps:</h2>
        <ul>
          <li>✅ React is loading</li>
          <li>⏳ Add back HeroUI components</li>
          <li>⏳ Add back complex UI</li>
        </ul>
      </div>
    </div>
  );
}

export default App;
