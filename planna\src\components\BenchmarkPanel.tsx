import React, { useState } from "react";

export function BenchmarkPanel() {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className="px-4 py-2">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-3">
          <h2 className="text-sm font-semibold text-slate-900">Benchmark Variables</h2>
          <span className="text-xs text-slate-500">Configure business assumptions</span>
        </div>
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="px-2 py-1 text-xs font-medium text-slate-600 hover:text-slate-900 border border-slate-300 rounded hover:bg-slate-50 transition-colors"
        >
          {isExpanded ? 'Hide' : 'Show'}
        </button>
      </div>

      {isExpanded && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 py-2">
          {/* Revenue Assumptions */}
          <div className="space-y-2">
            <h3 className="text-xs font-semibold text-slate-900 border-b border-slate-200 pb-1">Revenue</h3>

            <div>
              <h4 className="text-xs font-medium text-slate-700 mb-1">Pricing</h4>
              <div className="space-y-1">
                <div className="flex items-center gap-1">
                  <label className="text-xs text-slate-600 w-12">Basic:</label>
                  <input
                    type="number"
                    defaultValue={50}
                    className="flex-1 px-1 py-0.5 text-xs border border-slate-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                  />
                  <span className="text-xs text-slate-500">/mo</span>
                </div>
                <div className="flex items-center gap-1">
                  <label className="text-xs text-slate-600 w-12">Std:</label>
                  <input
                    type="number"
                    defaultValue={99}
                    className="flex-1 px-1 py-0.5 text-xs border border-slate-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                  />
                  <span className="text-xs text-slate-500">/mo</span>
                </div>
                <div className="flex items-center gap-1">
                  <label className="text-xs text-slate-600 w-12">Pro:</label>
                  <input
                    type="number"
                    defaultValue={199}
                    className="flex-1 px-1 py-0.5 text-xs border border-slate-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                  />
                  <span className="text-xs text-slate-500">/mo</span>
                </div>
                <div className="flex items-center gap-1">
                  <label className="text-xs text-slate-600 w-12">Ent:</label>
                  <input
                    type="number"
                    defaultValue={400}
                    className="flex-1 px-1 py-0.5 text-xs border border-slate-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                  />
                  <span className="text-xs text-slate-500">/mo</span>
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-xs font-medium text-slate-700 mb-1">Mix %</h4>
              <div className="grid grid-cols-4 gap-1">
                <input type="number" defaultValue={49} max={100} min={0} className="px-1 py-0.5 text-xs border border-slate-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500" />
                <input type="number" defaultValue={45} max={100} min={0} className="px-1 py-0.5 text-xs border border-slate-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500" />
                <input type="number" defaultValue={5} max={100} min={0} className="px-1 py-0.5 text-xs border border-slate-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500" />
                <input type="number" defaultValue={1} max={100} min={0} className="px-1 py-0.5 text-xs border border-slate-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500" />
              </div>
              <div className="mt-1 p-1 bg-blue-50 rounded text-xs">
                <span className="font-medium text-blue-900">ARPU: $83</span>
              </div>
            </div>
          </div>

          {/* Employee Productivity */}
          <div className="space-y-2">
            <h3 className="text-xs font-semibold text-slate-900 border-b border-slate-200 pb-1">Productivity</h3>

            <div>
              <h4 className="text-xs font-medium text-slate-700 mb-1">Sales</h4>
              <div className="space-y-1">
                <div className="flex items-center gap-1">
                  <label className="text-xs text-slate-600 w-16">Cust/rep:</label>
                  <input
                    type="number"
                    defaultValue={6.5}
                    step={0.1}
                    className="flex-1 px-1 py-0.5 text-xs border border-slate-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                  />
                  <span className="text-xs text-slate-500">/mo</span>
                </div>
                <div className="flex items-center gap-1">
                  <label className="text-xs text-slate-600 w-16">Ramp:</label>
                  <input
                    type="number"
                    defaultValue={2}
                    className="flex-1 px-1 py-0.5 text-xs border border-slate-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                  />
                  <span className="text-xs text-slate-500">mo</span>
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-xs font-medium text-slate-700 mb-1">Marketing</h4>
              <div className="space-y-1">
                <div className="flex items-center gap-1">
                  <label className="text-xs text-slate-600 w-16">Leads:</label>
                  <input
                    type="number"
                    defaultValue={200}
                    className="flex-1 px-1 py-0.5 text-xs border border-slate-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                  />
                  <span className="text-xs text-slate-500">/mo</span>
                </div>
                <div className="flex items-center gap-1">
                  <label className="text-xs text-slate-600 w-16">Conv:</label>
                  <input
                    type="number"
                    defaultValue={12}
                    max={100}
                    min={0}
                    className="flex-1 px-1 py-0.5 text-xs border border-slate-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                  />
                  <span className="text-xs text-slate-500">%</span>
                </div>
              </div>
            </div>
          </div>

          {/* Conversion Metrics */}
          <div className="space-y-2">
            <h3 className="text-xs font-semibold text-slate-900 border-b border-slate-200 pb-1">Conversion</h3>
            <div className="space-y-1">
              <div className="flex items-center gap-1">
                <label className="text-xs text-slate-600 w-16">Free→Paid:</label>
                <input
                  type="number"
                  defaultValue={10}
                  max={100}
                  min={0}
                  className="flex-1 px-1 py-0.5 text-xs border border-slate-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
                <span className="text-xs text-slate-500">%</span>
              </div>
              <div className="flex items-center gap-1">
                <label className="text-xs text-slate-600 w-16">Churn:</label>
                <input
                  type="number"
                  defaultValue={2}
                  max={100}
                  min={0}
                  step={0.1}
                  className="flex-1 px-1 py-0.5 text-xs border border-slate-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
                <span className="text-xs text-slate-500">%/mo</span>
              </div>
            </div>
          </div>

          {/* Salary Benchmarks */}
          <div className="space-y-2">
            <h3 className="text-xs font-semibold text-slate-900 border-b border-slate-200 pb-1">Salaries</h3>
            <div className="space-y-1">
              <div className="flex items-center gap-1">
                <label className="text-xs text-slate-600 w-12">Sales:</label>
                <input
                  type="number"
                  defaultValue={3000}
                  className="flex-1 px-1 py-0.5 text-xs border border-slate-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
              </div>
              <div className="flex items-center gap-1">
                <label className="text-xs text-slate-600 w-12">Mktg:</label>
                <input
                  type="number"
                  defaultValue={3000}
                  className="flex-1 px-1 py-0.5 text-xs border border-slate-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
              </div>
              <div className="flex items-center gap-1">
                <label className="text-xs text-slate-600 w-12">Dev:</label>
                <input
                  type="number"
                  defaultValue={4000}
                  className="flex-1 px-1 py-0.5 text-xs border border-slate-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
              </div>
              <div className="flex items-center gap-1">
                <label className="text-xs text-slate-600 w-12">Ops:</label>
                <input
                  type="number"
                  defaultValue={3000}
                  className="flex-1 px-1 py-0.5 text-xs border border-slate-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
              </div>
              <div className="flex items-center gap-1">
                <label className="text-xs text-slate-600 w-12">Lead:</label>
                <input
                  type="number"
                  defaultValue={5000}
                  className="flex-1 px-1 py-0.5 text-xs border border-slate-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
