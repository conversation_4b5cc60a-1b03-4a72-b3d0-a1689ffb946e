import React, { useState } from "react";
import {
  Button,
  Input,
  Card,
  CardBody,
  CardHeader,
  Chip,
  Toolt<PERSON>,
  Divider,
  Accordion,
  AccordionItem
} from "@heroui/react";
import { ChevronDownIcon, ChevronUpIcon, InfoIcon } from "@heroicons/react/24/outline";

export function BenchmarkPanel() {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <h2 className="text-lg font-semibold text-slate-900">Business Assumptions</h2>
            <Tooltip content="Configure key business metrics that apply to all months">
              <InfoIcon className="w-4 h-4 text-slate-400 cursor-help" />
            </Tooltip>
          </div>
          <Chip size="sm" variant="flat" color="primary">
            Global Settings
          </Chip>
        </div>
        <Button
          variant="flat"
          size="sm"
          onClick={() => setIsExpanded(!isExpanded)}
          endContent={isExpanded ? <ChevronUpIcon className="w-4 h-4" /> : <ChevronDownIcon className="w-4 h-4" />}
        >
          {isExpanded ? 'Collapse' : 'Configure'}
        </Button>
      </div>

      {isExpanded && (
        <div className="mt-4">
          <Accordion variant="splitted" className="px-0">
            {/* Revenue Assumptions */}
            <AccordionItem
              key="revenue"
              aria-label="Revenue Settings"
              title={
                <div className="flex items-center gap-2">
                  <span className="font-semibold">Revenue Settings</span>
                  <Chip size="sm" variant="flat" color="success">$83 ARPU</Chip>
                </div>
              }
              className="mb-2"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="p-4">
                  <CardHeader className="pb-2">
                    <h4 className="text-sm font-semibold text-slate-700">Pricing Tiers</h4>
                  </CardHeader>
                  <CardBody className="pt-0 space-y-3">
                    <Input
                      label="Basic Plan"
                      type="number"
                      defaultValue="50"
                      endContent={<span className="text-xs text-slate-500">/month</span>}
                      size="sm"
                      variant="bordered"
                    />
                    <Input
                      label="Standard Plan"
                      type="number"
                      defaultValue="99"
                      endContent={<span className="text-xs text-slate-500">/month</span>}
                      size="sm"
                      variant="bordered"
                    />
                    <Input
                      label="Professional Plan"
                      type="number"
                      defaultValue="199"
                      endContent={<span className="text-xs text-slate-500">/month</span>}
                      size="sm"
                      variant="bordered"
                    />
                    <Input
                      label="Enterprise Plan"
                      type="number"
                      defaultValue="400"
                      endContent={<span className="text-xs text-slate-500">/month</span>}
                      size="sm"
                      variant="bordered"
                    />
                  </CardBody>
                </Card>

                <Card className="p-4">
                  <CardHeader className="pb-2">
                    <div className="flex items-center gap-2">
                      <h4 className="text-sm font-semibold text-slate-700">Customer Mix</h4>
                      <Tooltip content="Percentage of customers in each pricing tier">
                        <InfoIcon className="w-4 h-4 text-slate-400 cursor-help" />
                      </Tooltip>
                    </div>
                  </CardHeader>
                  <CardBody className="pt-0 space-y-3">
                    <div className="grid grid-cols-2 gap-3">
                      <Input
                        label="Basic %"
                        type="number"
                        defaultValue="49"
                        min="0"
                        max="100"
                        endContent={<span className="text-xs text-slate-500">%</span>}
                        size="sm"
                        variant="bordered"
                      />
                      <Input
                        label="Standard %"
                        type="number"
                        defaultValue="45"
                        min="0"
                        max="100"
                        endContent={<span className="text-xs text-slate-500">%</span>}
                        size="sm"
                        variant="bordered"
                      />
                      <Input
                        label="Professional %"
                        type="number"
                        defaultValue="5"
                        min="0"
                        max="100"
                        endContent={<span className="text-xs text-slate-500">%</span>}
                        size="sm"
                        variant="bordered"
                      />
                      <Input
                        label="Enterprise %"
                        type="number"
                        defaultValue="1"
                        min="0"
                        max="100"
                        endContent={<span className="text-xs text-slate-500">%</span>}
                        size="sm"
                        variant="bordered"
                      />
                    </div>
                    <div className="mt-3 p-3 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
                      <div className="text-center">
                        <p className="text-xs text-slate-600 mb-1">Blended ARPU</p>
                        <p className="text-lg font-bold text-blue-600">$83</p>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              </div>
            </AccordionItem>

            {/* Team Productivity */}
            <AccordionItem
              key="productivity"
              aria-label="Team Productivity"
              title={
                <div className="flex items-center gap-2">
                  <span className="font-semibold">Team Productivity</span>
                  <Chip size="sm" variant="flat" color="warning">Performance Metrics</Chip>
                </div>
              }
              className="mb-2"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="p-4">
                  <CardHeader className="pb-2">
                    <div className="flex items-center gap-2">
                      <h4 className="text-sm font-semibold text-slate-700">Sales Team</h4>
                      <Tooltip content="How productive each sales rep is and their ramp-up time">
                        <InfoIcon className="w-4 h-4 text-slate-400 cursor-help" />
                      </Tooltip>
                    </div>
                  </CardHeader>
                  <CardBody className="pt-0 space-y-3">
                    <Input
                      label="Customers per Rep"
                      type="number"
                      defaultValue="6.5"
                      step="0.1"
                      endContent={<span className="text-xs text-slate-500">/month</span>}
                      size="sm"
                      variant="bordered"
                      description="Average new customers each rep closes monthly"
                    />
                    <Input
                      label="Ramp Time"
                      type="number"
                      defaultValue="2"
                      endContent={<span className="text-xs text-slate-500">months</span>}
                      size="sm"
                      variant="bordered"
                      description="Time for new reps to reach full productivity"
                    />
                    <div className="text-xs text-slate-500 bg-slate-50 p-2 rounded">
                      <strong>Ramp Schedule:</strong> 50% (Month 1), 75% (Month 2), 100% (Month 3+)
                    </div>
                  </CardBody>
                </Card>

                <Card className="p-4">
                  <CardHeader className="pb-2">
                    <div className="flex items-center gap-2">
                      <h4 className="text-sm font-semibold text-slate-700">Marketing Team</h4>
                      <Tooltip content="Lead generation and conversion performance">
                        <InfoIcon className="w-4 h-4 text-slate-400 cursor-help" />
                      </Tooltip>
                    </div>
                  </CardHeader>
                  <CardBody className="pt-0 space-y-3">
                    <Input
                      label="Leads per Marketer"
                      type="number"
                      defaultValue="200"
                      endContent={<span className="text-xs text-slate-500">/month</span>}
                      size="sm"
                      variant="bordered"
                      description="Qualified leads generated monthly"
                    />
                    <Input
                      label="Lead Conversion Rate"
                      type="number"
                      defaultValue="12"
                      min="0"
                      max="100"
                      endContent={<span className="text-xs text-slate-500">%</span>}
                      size="sm"
                      variant="bordered"
                      description="Percentage of leads that become customers"
                    />
                  </CardBody>
                </Card>
              </div>
            </AccordionItem>

            {/* Conversion & Retention */}
            <AccordionItem
              key="conversion"
              aria-label="Conversion & Retention"
              title={
                <div className="flex items-center gap-2">
                  <span className="font-semibold">Conversion & Retention</span>
                  <Chip size="sm" variant="flat" color="secondary">Customer Lifecycle</Chip>
                </div>
              }
              className="mb-2"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="p-4">
                  <CardHeader className="pb-2">
                    <h4 className="text-sm font-semibold text-slate-700">Conversion Rates</h4>
                  </CardHeader>
                  <CardBody className="pt-0 space-y-3">
                    <Input
                      label="Free to Paid Conversion"
                      type="number"
                      defaultValue="10"
                      min="0"
                      max="100"
                      endContent={<span className="text-xs text-slate-500">%</span>}
                      size="sm"
                      variant="bordered"
                      description="Percentage of free users who upgrade"
                    />
                    <Input
                      label="Monthly Churn Rate"
                      type="number"
                      defaultValue="2"
                      min="0"
                      max="100"
                      step="0.1"
                      endContent={<span className="text-xs text-slate-500">%/month</span>}
                      size="sm"
                      variant="bordered"
                      description="Percentage of customers lost monthly"
                    />
                  </CardBody>
                </Card>

                <Card className="p-4">
                  <CardHeader className="pb-2">
                    <h4 className="text-sm font-semibold text-slate-700">Starting Conditions</h4>
                  </CardHeader>
                  <CardBody className="pt-0 space-y-3">
                    <Input
                      label="Initial Cash"
                      type="number"
                      defaultValue="151500"
                      startContent={<span className="text-xs text-slate-500">$</span>}
                      size="sm"
                      variant="bordered"
                      description="Starting cash position"
                    />
                    <Input
                      label="Fixed Monthly Costs"
                      type="number"
                      defaultValue="2000"
                      startContent={<span className="text-xs text-slate-500">$</span>}
                      endContent={<span className="text-xs text-slate-500">/month</span>}
                      size="sm"
                      variant="bordered"
                      description="Office, software, other fixed expenses"
                    />
                  </CardBody>
                </Card>
              </div>
            </AccordionItem>

            {/* Team Salaries */}
            <AccordionItem
              key="salaries"
              aria-label="Team Salaries"
              title={
                <div className="flex items-center gap-2">
                  <span className="font-semibold">Team Salaries</span>
                  <Chip size="sm" variant="flat" color="danger">Monthly Costs</Chip>
                </div>
              }
            >
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Input
                  label="Sales Rep"
                  type="number"
                  defaultValue="3000"
                  startContent={<span className="text-xs text-slate-500">$</span>}
                  endContent={<span className="text-xs text-slate-500">/month</span>}
                  size="sm"
                  variant="bordered"
                />
                <Input
                  label="Marketing"
                  type="number"
                  defaultValue="3000"
                  startContent={<span className="text-xs text-slate-500">$</span>}
                  endContent={<span className="text-xs text-slate-500">/month</span>}
                  size="sm"
                  variant="bordered"
                />
                <Input
                  label="Developer"
                  type="number"
                  defaultValue="4000"
                  startContent={<span className="text-xs text-slate-500">$</span>}
                  endContent={<span className="text-xs text-slate-500">/month</span>}
                  size="sm"
                  variant="bordered"
                />
                <Input
                  label="Operations"
                  type="number"
                  defaultValue="3000"
                  startContent={<span className="text-xs text-slate-500">$</span>}
                  endContent={<span className="text-xs text-slate-500">/month</span>}
                  size="sm"
                  variant="bordered"
                />
                <Input
                  label="Leadership"
                  type="number"
                  defaultValue="5000"
                  startContent={<span className="text-xs text-slate-500">$</span>}
                  endContent={<span className="text-xs text-slate-500">/month</span>}
                  size="sm"
                  variant="bordered"
                />
              </div>
            </AccordionItem>
          </Accordion>
        </div>
      )}
    </div>
  );
}
