using System.ComponentModel;

namespace Planna;

public class BusinessLogic : INotifyPropertyChanged
{
    // Pricing Tiers
    private decimal _basicPlan = 50;
    private decimal _standardPlan = 99;
    private decimal _professionalPlan = 199;
    private decimal _enterprisePlan = 400;

    // Customer Mix (percentages)
    private decimal _basicMixPercent = 49;
    private decimal _standardMixPercent = 45;
    private decimal _professionalMixPercent = 5;
    private decimal _enterpriseMixPercent = 1;

    // Team Productivity
    private decimal _customersPerSalesRep = 6.5m;
    private int _salesRampMonths = 2;
    private int _leadsPerMarketer = 200;
    private decimal _leadConversionPercent = 12;

    // Salaries
    private decimal _salesRepSalary = 3000;
    private decimal _marketingSalary = 3000;
    private decimal _developerSalary = 4000;
    private decimal _operationsSalary = 3000;
    private decimal _leadershipSalary = 5000;
    private decimal _fixedMonthlyCosts = 2000;

    // Financial Settings
    private decimal _freeToPayConversionPercent = 10;
    private decimal _monthlyChurnPercent = 2;
    private decimal _initialCash = 151500;

    #region Properties

    public decimal BasicPlan
    {
        get => _basicPlan;
        set { _basicPlan = value; OnPropertyChanged(); CalculateARPU(); }
    }

    public decimal StandardPlan
    {
        get => _standardPlan;
        set { _standardPlan = value; OnPropertyChanged(); CalculateARPU(); }
    }

    public decimal ProfessionalPlan
    {
        get => _professionalPlan;
        set { _professionalPlan = value; OnPropertyChanged(); CalculateARPU(); }
    }

    public decimal EnterprisePlan
    {
        get => _enterprisePlan;
        set { _enterprisePlan = value; OnPropertyChanged(); CalculateARPU(); }
    }

    public decimal BasicMixPercent
    {
        get => _basicMixPercent;
        set { _basicMixPercent = value; OnPropertyChanged(); CalculateARPU(); ValidateCustomerMix(); }
    }

    public decimal StandardMixPercent
    {
        get => _standardMixPercent;
        set { _standardMixPercent = value; OnPropertyChanged(); CalculateARPU(); ValidateCustomerMix(); }
    }

    public decimal ProfessionalMixPercent
    {
        get => _professionalMixPercent;
        set { _professionalMixPercent = value; OnPropertyChanged(); CalculateARPU(); ValidateCustomerMix(); }
    }

    public decimal EnterpriseMixPercent
    {
        get => _enterpriseMixPercent;
        set { _enterpriseMixPercent = value; OnPropertyChanged(); CalculateARPU(); ValidateCustomerMix(); }
    }

    public decimal CustomersPerSalesRep
    {
        get => _customersPerSalesRep;
        set { _customersPerSalesRep = value; OnPropertyChanged(); }
    }

    public int SalesRampMonths
    {
        get => _salesRampMonths;
        set { _salesRampMonths = value; OnPropertyChanged(); }
    }

    public int LeadsPerMarketer
    {
        get => _leadsPerMarketer;
        set { _leadsPerMarketer = value; OnPropertyChanged(); }
    }

    public decimal LeadConversionPercent
    {
        get => _leadConversionPercent;
        set { _leadConversionPercent = value; OnPropertyChanged(); }
    }

    public decimal SalesRepSalary
    {
        get => _salesRepSalary;
        set { _salesRepSalary = value; OnPropertyChanged(); }
    }

    public decimal MarketingSalary
    {
        get => _marketingSalary;
        set { _marketingSalary = value; OnPropertyChanged(); }
    }

    public decimal DeveloperSalary
    {
        get => _developerSalary;
        set { _developerSalary = value; OnPropertyChanged(); }
    }

    public decimal OperationsSalary
    {
        get => _operationsSalary;
        set { _operationsSalary = value; OnPropertyChanged(); }
    }

    public decimal LeadershipSalary
    {
        get => _leadershipSalary;
        set { _leadershipSalary = value; OnPropertyChanged(); }
    }

    public decimal FixedMonthlyCosts
    {
        get => _fixedMonthlyCosts;
        set { _fixedMonthlyCosts = value; OnPropertyChanged(); }
    }

    public decimal FreeToPayConversionPercent
    {
        get => _freeToPayConversionPercent;
        set { _freeToPayConversionPercent = value; OnPropertyChanged(); }
    }

    public decimal MonthlyChurnPercent
    {
        get => _monthlyChurnPercent;
        set { _monthlyChurnPercent = value; OnPropertyChanged(); }
    }

    public decimal InitialCash
    {
        get => _initialCash;
        set { _initialCash = value; OnPropertyChanged(); }
    }

    #endregion

    #region Calculated Properties

    public decimal BlendedARPU { get; private set; }
    public decimal TotalCustomerMixPercent { get; private set; }
    public bool IsCustomerMixValid { get; private set; }
    public string CustomerMixValidationMessage { get; private set; } = "";

    #endregion

    #region Calculations

    private void CalculateARPU()
    {
        BlendedARPU = (BasicPlan * BasicMixPercent / 100) +
                      (StandardPlan * StandardMixPercent / 100) +
                      (ProfessionalPlan * ProfessionalMixPercent / 100) +
                      (EnterprisePlan * EnterpriseMixPercent / 100);

        OnPropertyChanged(nameof(BlendedARPU));
    }

    private void ValidateCustomerMix()
    {
        TotalCustomerMixPercent = BasicMixPercent + StandardMixPercent + ProfessionalMixPercent + EnterpriseMixPercent;
        IsCustomerMixValid = Math.Abs(TotalCustomerMixPercent - 100) < 0.01m;

        CustomerMixValidationMessage = TotalCustomerMixPercent switch
        {
            < 100 => $"Total: {TotalCustomerMixPercent:F1}% (Need {100 - TotalCustomerMixPercent:F1}% more)",
            > 100 => $"Total: {TotalCustomerMixPercent:F1}% (Reduce by {TotalCustomerMixPercent - 100:F1}%)",
            _ => $"Total: {TotalCustomerMixPercent:F1}% ✓"
        };

        OnPropertyChanged(nameof(TotalCustomerMixPercent));
        OnPropertyChanged(nameof(IsCustomerMixValid));
        OnPropertyChanged(nameof(CustomerMixValidationMessage));
    }

    public decimal CalculateMonthlyBurn(MonthlyPlanData monthData)
    {
        var teamCost = (monthData.SalesReps * SalesRepSalary) +
                       (monthData.MarketingTeam * MarketingSalary) +
                       (monthData.DevTeam * DeveloperSalary) +
                       (monthData.OpsTeam * OperationsSalary) +
                       (monthData.LeadershipTeam * LeadershipSalary);

        return teamCost + monthData.EventsCost + FixedMonthlyCosts;
    }

    public decimal CalculateMRR(int totalCustomers)
    {
        return totalCustomers * BlendedARPU;
    }

    public decimal CalculateCustomerAcquisitionCost(decimal salesCost, decimal marketingCost, decimal eventsCost, int newCustomers)
    {
        if (newCustomers == 0) return 0;
        return (salesCost + marketingCost + eventsCost) / newCustomers;
    }

    public decimal CalculateLifetimeValue(decimal avgCustomerLifeMonths)
    {
        return BlendedARPU * avgCustomerLifeMonths;
    }

    public decimal CalculateRunwayMonths(decimal currentCash, decimal monthlyBurn)
    {
        if (monthlyBurn <= 0) return decimal.MaxValue;
        return currentCash / monthlyBurn;
    }

    #endregion

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}
