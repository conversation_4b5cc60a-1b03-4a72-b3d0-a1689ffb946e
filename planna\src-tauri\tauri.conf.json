{"$schema": "https://schema.tauri.app/config/2", "productName": "planna", "version": "0.1.0", "identifier": "com.planna.app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"withGlobalTauri": true, "windows": [{"title": "Planna - Growth Planning Tool", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "webSecurity": false, "devtools": true}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}