{"name": "planna", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@heroicons/react": "^2.2.0", "@heroui/react": "^2.7.11", "@hookform/resolvers": "^5.1.1", "@tailwindcss/postcss": "^4.1.11", "@tauri-apps/api": "^2.6.0", "@tauri-apps/plugin-dialog": "^2.3.0", "@tauri-apps/plugin-fs": "^2.4.0", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-sql": "^2.3.0", "@types/papaparse": "^5.3.16", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "date-fns": "^4.1.0", "framer-motion": "^12.23.1", "jspdf": "^3.0.1", "papaparse": "^5.5.3", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "recharts": "^3.1.0", "tailwindcss": "^4.1.11", "zod": "^3.25.76", "zustand": "^5.0.6"}, "devDependencies": {"@tauri-apps/cli": "^2", "typescript": "~5.6.2", "vite": "^6.0.3"}}