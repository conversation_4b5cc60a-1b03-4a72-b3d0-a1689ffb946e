﻿using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using OxyPlot;
using OxyPlot.Series;
using OxyPlot.Axes;

namespace Planna;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    public ObservableCollection<MonthlyPlanData> MonthlyData { get; set; }

    public MainWindow()
    {
        InitializeComponent();
        InitializeData();
        SetupChart();
    }

    private void InitializeData()
    {
        MonthlyData = new ObservableCollection<MonthlyPlanData>
        {
            new MonthlyPlanData { Month = "Aug '25", SalesReps = 1, MarketingTeam = 0, DevTeam = 0, OpsTeam = 0, LeadershipTeam = 0, EventsCost = 2000 },
            new MonthlyPlanData { Month = "Sep '25", SalesReps = 2, MarketingTeam = 1, DevTeam = 0, OpsTeam = 0, LeadershipTeam = 0, EventsCost = 3500 },
            new MonthlyPlanData { Month = "Oct '25", SalesReps = 3, MarketingTeam = 1, DevTeam = 0, OpsTeam = 0, LeadershipTeam = 0, EventsCost = 2500 },
            new MonthlyPlanData { Month = "Nov '25", SalesReps = 4, MarketingTeam = 2, DevTeam = 0, OpsTeam = 0, LeadershipTeam = 0, EventsCost = 5000 },
            new MonthlyPlanData { Month = "Dec '25", SalesReps = 5, MarketingTeam = 3, DevTeam = 0, OpsTeam = 0, LeadershipTeam = 0, EventsCost = 8000 },
            new MonthlyPlanData { Month = "Jan '26", SalesReps = 5, MarketingTeam = 4, DevTeam = 0, OpsTeam = 1, LeadershipTeam = 0, EventsCost = 10000 },
            new MonthlyPlanData { Month = "Feb '26", SalesReps = 5, MarketingTeam = 4, DevTeam = 0, OpsTeam = 1, LeadershipTeam = 0, EventsCost = 5000 },
            new MonthlyPlanData { Month = "Mar '26", SalesReps = 6, MarketingTeam = 4, DevTeam = 1, OpsTeam = 1, LeadershipTeam = 0, EventsCost = 7000 },
            new MonthlyPlanData { Month = "Apr '26", SalesReps = 7, MarketingTeam = 5, DevTeam = 1, OpsTeam = 1, LeadershipTeam = 1, EventsCost = 6000 },
            new MonthlyPlanData { Month = "May '26", SalesReps = 8, MarketingTeam = 5, DevTeam = 2, OpsTeam = 2, LeadershipTeam = 1, EventsCost = 8000 }
        };

        MonthlyPlanningGrid.ItemsSource = MonthlyData;
    }

    private void SetupChart()
    {
        var plotModel = new PlotModel
        {
            Title = "Growth Metrics Over Time",
            Background = OxyColors.White
        };

        // Add axes
        plotModel.Axes.Add(new CategoryAxis
        {
            Position = AxisPosition.Bottom,
            Title = "Month",
            ItemsSource = new[] { "Aug '25", "Sep '25", "Oct '25", "Nov '25", "Dec '25", "Jan '26", "Feb '26", "Mar '26", "Apr '26", "May '26" }
        });

        plotModel.Axes.Add(new LinearAxis
        {
            Position = AxisPosition.Left,
            Title = "Value",
            Minimum = 0
        });

        // Sample data for MRR
        var mrrSeries = new LineSeries
        {
            Title = "MRR ($)",
            Color = OxyColors.Blue,
            StrokeThickness = 3,
            MarkerType = MarkerType.Circle,
            MarkerSize = 6
        };

        var mrrData = new[] { 0, 5400, 12000, 19100, 28300, 39600, 48100, 58200, 69800, 83500 };
        for (int i = 0; i < mrrData.Length; i++)
        {
            mrrSeries.Points.Add(new DataPoint(i, mrrData[i]));
        }

        // Sample data for Customers
        var customerSeries = new LineSeries
        {
            Title = "Customers",
            Color = OxyColors.Green,
            StrokeThickness = 3,
            MarkerType = MarkerType.Circle,
            MarkerSize = 6
        };

        var customerData = new[] { 0, 65, 145, 230, 341, 477, 579, 701, 841, 1006 };
        for (int i = 0; i < customerData.Length; i++)
        {
            customerSeries.Points.Add(new DataPoint(i, customerData[i]));
        }

        plotModel.Series.Add(mrrSeries);
        plotModel.Series.Add(customerSeries);

        GrowthChart.Model = plotModel;
    }
}