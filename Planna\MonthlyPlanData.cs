using System.ComponentModel;

namespace Planna;

public class MonthlyPlanData : INotifyPropertyChanged
{
    private int _salesReps;
    private int _marketingTeam;
    private int _devTeam;
    private int _opsTeam;
    private int _leadershipTeam;
    private decimal _eventsCost;

    public string Month { get; set; } = string.Empty;

    public int SalesReps
    {
        get => _salesReps;
        set
        {
            _salesReps = value;
            OnPropertyChanged(nameof(SalesReps));
            UpdateTotalBurn();
        }
    }

    public int MarketingTeam
    {
        get => _marketingTeam;
        set
        {
            _marketingTeam = value;
            OnPropertyChanged(nameof(MarketingTeam));
            UpdateTotalBurn();
        }
    }

    public int DevTeam
    {
        get => _devTeam;
        set
        {
            _devTeam = value;
            OnPropertyChanged(nameof(DevTeam));
            UpdateTotalBurn();
        }
    }

    public int OpsTeam
    {
        get => _opsTeam;
        set
        {
            _opsTeam = value;
            OnPropertyChanged(nameof(OpsTeam));
            UpdateTotalBurn();
        }
    }

    public int LeadershipTeam
    {
        get => _leadershipTeam;
        set
        {
            _leadershipTeam = value;
            OnPropertyChanged(nameof(LeadershipTeam));
            UpdateTotalBurn();
        }
    }

    public decimal EventsCost
    {
        get => _eventsCost;
        set
        {
            _eventsCost = value;
            OnPropertyChanged(nameof(EventsCost));
            UpdateTotalBurn();
        }
    }

    public decimal TotalBurn { get; private set; }
    public string BurnLevel { get; private set; } = "Low";

    private void UpdateTotalBurn()
    {
        var teamCost = (SalesReps * 3000) + (MarketingTeam * 3000) + (DevTeam * 4000) + (OpsTeam * 3000) + (LeadershipTeam * 5000);
        TotalBurn = teamCost + EventsCost + 2000; // +2000 for fixed costs

        BurnLevel = TotalBurn switch
        {
            > 50000 => "High",
            > 30000 => "Medium",
            _ => "Low"
        };

        OnPropertyChanged(nameof(TotalBurn));
        OnPropertyChanged(nameof(BurnLevel));
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged(string propertyName)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}
