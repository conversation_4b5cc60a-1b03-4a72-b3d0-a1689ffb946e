import React from "react";
import {
  <PERSON>,
  CardBody,
  CardHeader,
  Chip,
  Progress,
  Divider,
  Tooltip
} from "@heroui/react";
import {
  CurrencyDollarIcon,
  UsersIcon,
  ChartBarIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  InformationCircleIcon
} from "@heroicons/react/24/outline";

export function UnitEconomicsPanel() {
  // Sample data - this will be replaced with real calculations
  const currentMonth = "Feb 2026";
  const metrics = {
    totalCustomers: 578,
    newThisMonth: 164,
    blendedARPU: 83,
    mrr: 48061,
    arr: 576732,
    salesTeamCost: 15000,
    marketingCost: 12000,
    eventsThisMonth: 5000,
    totalCAC: 195,
    avgCustomerLife: 30,
    ltv: 2490,
    ltvCacRatio: 12.8,
    paybackPeriod: 2.3,
    cashOnHand: 89425,
    monthlyBurn: 32000,
    runway: 2.8,
    breakEven: "May 2026",
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat("en-US").format(value);
  };

  const getHealthColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value >= thresholds.good) return "success";
    if (value >= thresholds.warning) return "warning";
    return "danger";
  };

  const getHealthIcon = (value: number, thresholds: { good: number; warning: number }) => {
    if (value >= thresholds.good) return <CheckCircleIcon className="w-4 h-4" />;
    if (value >= thresholds.warning) return <ExclamationTriangleIcon className="w-4 h-4" />;
    return <ExclamationTriangleIcon className="w-4 h-4" />;
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <CardHeader className="px-6 py-4">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg">
            <ChartBarIcon className="w-5 h-5 text-white" />
          </div>
          <div>
            <h2 className="text-lg font-semibold text-slate-900">Unit Economics</h2>
            <div className="flex items-center gap-2">
              <p className="text-sm text-slate-500">{currentMonth}</p>
              <Chip size="sm" variant="flat" color="primary">Live Data</Chip>
            </div>
          </div>
        </div>
      </CardHeader>

      <Divider />

      <div className="flex-1 overflow-y-auto p-6 space-y-6">
        {/* Customer Metrics */}
        <Card className="border-0 shadow-sm">
          <CardHeader className="pb-2">
            <div className="flex items-center gap-2">
              <UsersIcon className="w-5 h-5 text-blue-500" />
              <h3 className="text-sm font-semibold text-slate-900">Customer Metrics</h3>
            </div>
          </CardHeader>
          <CardBody className="pt-0 space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg">
                <p className="text-xs text-slate-600 mb-1">Total Customers</p>
                <p className="text-xl font-bold text-blue-600">{formatNumber(metrics.totalCustomers)}</p>
              </div>
              <div className="text-center p-3 bg-gradient-to-br from-green-50 to-green-100 rounded-lg">
                <p className="text-xs text-slate-600 mb-1">New This Month</p>
                <p className="text-xl font-bold text-green-600">+{formatNumber(metrics.newThisMonth)}</p>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex justify-between items-center p-2 bg-slate-50 rounded">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-slate-600">Blended ARPU</span>
                  <Tooltip content="Average Revenue Per User across all pricing tiers">
                    <InformationCircleIcon className="w-4 h-4 text-slate-400 cursor-help" />
                  </Tooltip>
                </div>
                <span className="text-sm font-semibold text-slate-900">{formatCurrency(metrics.blendedARPU)}</span>
              </div>

              <div className="flex justify-between items-center p-2 bg-blue-50 rounded">
                <span className="text-sm text-slate-600">Monthly Recurring Revenue</span>
                <span className="text-sm font-bold text-blue-600">{formatCurrency(metrics.mrr)}</span>
              </div>

              <div className="flex justify-between items-center p-2 bg-purple-50 rounded">
                <span className="text-sm text-slate-600">Annual Recurring Revenue</span>
                <span className="text-sm font-bold text-purple-600">{formatCurrency(metrics.arr)}</span>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Acquisition Costs */}
        <Card className="border-0 shadow-sm">
          <CardHeader className="pb-2">
            <div className="flex items-center gap-2">
              <CurrencyDollarIcon className="w-5 h-5 text-orange-500" />
              <h3 className="text-sm font-semibold text-slate-900">Customer Acquisition</h3>
            </div>
          </CardHeader>
          <CardBody className="pt-0 space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between items-center p-2 bg-slate-50 rounded">
                <span className="text-sm text-slate-600">Sales Team Cost</span>
                <span className="text-sm font-semibold text-slate-900">{formatCurrency(metrics.salesTeamCost)}</span>
              </div>
              <div className="flex justify-between items-center p-2 bg-slate-50 rounded">
                <span className="text-sm text-slate-600">Marketing Cost</span>
                <span className="text-sm font-semibold text-slate-900">{formatCurrency(metrics.marketingCost)}</span>
              </div>
              <div className="flex justify-between items-center p-2 bg-slate-50 rounded">
                <span className="text-sm text-slate-600">Events & Other</span>
                <span className="text-sm font-semibold text-slate-900">{formatCurrency(metrics.eventsThisMonth)}</span>
              </div>
            </div>

            <Divider />

            <div className="text-center p-4 bg-gradient-to-br from-red-50 to-orange-50 rounded-lg border border-red-200">
              <div className="flex items-center justify-center gap-2 mb-2">
                <span className="text-sm font-semibold text-slate-700">Customer Acquisition Cost</span>
                <Tooltip content="Total cost to acquire one new customer">
                  <InformationCircleIcon className="w-4 h-4 text-slate-400 cursor-help" />
                </Tooltip>
              </div>
              <p className="text-2xl font-bold text-red-600">{formatCurrency(metrics.totalCAC)}</p>
            </div>
          </CardBody>
        </Card>

        {/* Lifetime Value */}
        <Card className="border-0 shadow-sm">
          <CardHeader className="pb-2">
            <div className="flex items-center gap-2">
              <ChartBarIcon className="w-5 h-5 text-green-500" />
              <h3 className="text-sm font-semibold text-slate-900">Lifetime Value</h3>
            </div>
          </CardHeader>
          <CardBody className="pt-0 space-y-4">
            <div className="grid grid-cols-2 gap-3">
              <div className="text-center p-3 bg-slate-50 rounded-lg">
                <p className="text-xs text-slate-600 mb-1">Avg Customer Life</p>
                <p className="text-lg font-bold text-slate-900">{metrics.avgCustomerLife}mo</p>
              </div>
              <div className="text-center p-3 bg-gradient-to-br from-green-50 to-green-100 rounded-lg">
                <p className="text-xs text-slate-600 mb-1">Customer LTV</p>
                <p className="text-lg font-bold text-green-600">{formatCurrency(metrics.ltv)}</p>
              </div>
            </div>

            <div className="space-y-3">
              <div className="p-3 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-semibold text-slate-700">LTV:CAC Ratio</span>
                    <Tooltip content="Healthy ratio should be 3:1 or higher">
                      <InformationCircleIcon className="w-4 h-4 text-slate-400 cursor-help" />
                    </Tooltip>
                  </div>
                  {getHealthIcon(metrics.ltvCacRatio, { good: 3, warning: 2 })}
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold text-blue-600">{metrics.ltvCacRatio}:1</span>
                  <Chip
                    size="sm"
                    color={getHealthColor(metrics.ltvCacRatio, { good: 3, warning: 2 })}
                    variant="flat"
                  >
                    {metrics.ltvCacRatio >= 3 ? 'Healthy' : metrics.ltvCacRatio >= 2 ? 'Warning' : 'Critical'}
                  </Chip>
                </div>
              </div>

              <div className="flex justify-between items-center p-2 bg-slate-50 rounded">
                <span className="text-sm text-slate-600">Payback Period</span>
                <span className="text-sm font-semibold text-slate-900">{metrics.paybackPeriod} months</span>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Financial Health */}
        <Card className="border-0 shadow-sm">
          <CardHeader className="pb-2">
            <div className="flex items-center gap-2">
              <ClockIcon className="w-5 h-5 text-purple-500" />
              <h3 className="text-sm font-semibold text-slate-900">Financial Health</h3>
            </div>
          </CardHeader>
          <CardBody className="pt-0 space-y-4">
            <div className="grid grid-cols-2 gap-3">
              <div className="text-center p-3 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg">
                <p className="text-xs text-slate-600 mb-1">Cash on Hand</p>
                <p className="text-lg font-bold text-blue-600">{formatCurrency(metrics.cashOnHand)}</p>
              </div>
              <div className="text-center p-3 bg-gradient-to-br from-red-50 to-red-100 rounded-lg">
                <p className="text-xs text-slate-600 mb-1">Monthly Burn</p>
                <p className="text-lg font-bold text-red-600">{formatCurrency(metrics.monthlyBurn)}</p>
              </div>
            </div>

            <div className="space-y-3">
              <div className="p-3 bg-gradient-to-r from-orange-50 to-red-50 rounded-lg border border-orange-200">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-semibold text-slate-700">Runway</span>
                    <Tooltip content="Months of cash remaining at current burn rate">
                      <InformationCircleIcon className="w-4 h-4 text-slate-400 cursor-help" />
                    </Tooltip>
                  </div>
                  {getHealthIcon(metrics.runway, { good: 6, warning: 3 })}
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold text-orange-600">{metrics.runway} months</span>
                  <Chip
                    size="sm"
                    color={getHealthColor(metrics.runway, { good: 6, warning: 3 })}
                    variant="flat"
                  >
                    {metrics.runway >= 6 ? 'Safe' : metrics.runway >= 3 ? 'Warning' : 'Critical'}
                  </Chip>
                </div>
                <Progress
                  value={Math.min((metrics.runway / 12) * 100, 100)}
                  color={getHealthColor(metrics.runway, { good: 6, warning: 3 })}
                  className="mt-2"
                  size="sm"
                />
              </div>

              <div className="flex justify-between items-center p-2 bg-green-50 rounded">
                <span className="text-sm text-slate-600">Break-even Target</span>
                <span className="text-sm font-semibold text-green-600">{metrics.breakEven}</span>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
}
