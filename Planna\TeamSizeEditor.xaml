<UserControl x:Class="Planna.TeamSizeEditor"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:Planna"
             mc:Ignorable="d" 
             d:DesignHeight="30" d:DesignWidth="120">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="Auto"/>
        </Grid.ColumnDefinitions>
        
        <Button Grid.Column="0" Content="−" Width="25" Height="25" 
                FontSize="14" FontWeight="Bold"
                Click="DecrementButton_Click"
                Background="LightGray" BorderBrush="Gray"/>
        
        <TextBox Grid.Column="1" x:Name="ValueTextBox" 
                 Text="{Binding Value, RelativeSource={RelativeSource AncestorType=local:TeamSizeEditor}, UpdateSourceTrigger=PropertyChanged}"
                 HorizontalContentAlignment="Center" VerticalContentAlignment="Center"
                 Margin="2,0" FontWeight="SemiBold"/>
        
        <Button Grid.Column="2" Content="+" Width="25" Height="25" 
                FontSize="14" FontWeight="Bold"
                Click="IncrementButton_Click"
                Background="LightGreen" BorderBrush="Green"/>
    </Grid>
</UserControl>
