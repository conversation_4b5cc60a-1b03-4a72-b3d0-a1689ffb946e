import React, { useState } from "react";
import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Input,
  Card,
  CardBody,
  CardHeader,
  <PERSON>,
  <PERSON><PERSON>,
  Toolt<PERSON>
} from "@heroui/react";
import {
  CalendarIcon,
  PlusIcon,
  MinusIcon,
  InformationCircleIcon
} from "@heroicons/react/24/outline";

interface MonthData {
  month: string;
  salesReps: number;
  marketingTeam: number;
  devTeam: number;
  opsTeam: number;
  leadershipTeam: number;
  eventsCost: number;
  totalBurn: number;
}

const initialData: MonthData[] = [
  { month: "Aug '25", salesReps: 1, marketingTeam: 0, devTeam: 0, opsTeam: 0, leadershipTeam: 0, eventsCost: 2000, totalBurn: 5000 },
  { month: "Sep '25", salesReps: 2, marketingTeam: 1, devTeam: 0, opsTeam: 0, leadershipTeam: 0, eventsCost: 3500, totalBurn: 11500 },
  { month: "Oct '25", salesReps: 3, marketingTeam: 1, devTeam: 0, opsTeam: 0, leadershipTeam: 0, eventsCost: 2500, totalBurn: 14500 },
  { month: "Nov '25", salesReps: 4, marketingTeam: 2, devTeam: 0, opsTeam: 0, leadershipTeam: 0, eventsCost: 5000, totalBurn: 23000 },
  { month: "Dec '25", salesReps: 5, marketingTeam: 3, devTeam: 0, opsTeam: 0, leadershipTeam: 0, eventsCost: 8000, totalBurn: 32000 },
  { month: "Jan '26", salesReps: 5, marketingTeam: 4, devTeam: 0, opsTeam: 1, leadershipTeam: 0, eventsCost: 10000, totalBurn: 40000 },
  { month: "Feb '26", salesReps: 5, marketingTeam: 4, devTeam: 0, opsTeam: 1, leadershipTeam: 0, eventsCost: 5000, totalBurn: 35000 },
  { month: "Mar '26", salesReps: 6, marketingTeam: 4, devTeam: 1, opsTeam: 1, leadershipTeam: 0, eventsCost: 7000, totalBurn: 42000 },
  { month: "Apr '26", salesReps: 7, marketingTeam: 5, devTeam: 1, opsTeam: 1, leadershipTeam: 1, eventsCost: 6000, totalBurn: 53000 },
  { month: "May '26", salesReps: 8, marketingTeam: 5, devTeam: 2, opsTeam: 2, leadershipTeam: 1, eventsCost: 8000, totalBurn: 64000 },
];

export function MonthlyInputTable() {
  const [data, setData] = useState<MonthData[]>(initialData);

  const updateCell = (rowIndex: number, field: keyof MonthData, value: string) => {
    const newData = [...data];
    const numValue = field === 'month' ? value : parseInt(value) || 0;
    newData[rowIndex] = { ...newData[rowIndex], [field]: numValue };

    // Recalculate total burn (simplified calculation)
    if (field !== 'month' && field !== 'totalBurn') {
      const row = newData[rowIndex];
      const teamCost = (row.salesReps * 3000) + (row.marketingTeam * 3000) +
                      (row.devTeam * 4000) + (row.opsTeam * 3000) +
                      (row.leadershipTeam * 5000);
      newData[rowIndex].totalBurn = teamCost + row.eventsCost + 2000; // +2000 for fixed costs
    }

    setData(newData);
  };

  const adjustTeamSize = (rowIndex: number, field: keyof MonthData, delta: number) => {
    const currentValue = data[rowIndex][field] as number;
    const newValue = Math.max(0, currentValue + delta);
    updateCell(rowIndex, field, newValue.toString());
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const getBurnColor = (burn: number) => {
    if (burn > 50000) return "danger";
    if (burn > 30000) return "warning";
    return "success";
  };

  const columns = [
    { key: "month", label: "Month", description: "Planning period" },
    { key: "salesReps", label: "Sales Team", description: "Number of sales representatives" },
    { key: "marketingTeam", label: "Marketing", description: "Marketing team members" },
    { key: "devTeam", label: "Development", description: "Software developers" },
    { key: "opsTeam", label: "Operations", description: "Operations team members" },
    { key: "leadershipTeam", label: "Leadership", description: "Management and leadership roles" },
    { key: "eventsCost", label: "Events Budget", description: "Monthly events and marketing spend" },
    { key: "totalBurn", label: "Total Monthly Burn", description: "Total monthly expenses" },
  ];

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <CardHeader className="px-6 py-4">
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg">
              <CalendarIcon className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-slate-900">Monthly Team Planning</h2>
              <p className="text-sm text-slate-500">Plan team growth and expenses month by month</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Chip size="sm" variant="flat" color="secondary">
              {data.length} months planned
            </Chip>
            <Button size="sm" variant="flat" color="primary">
              Add Month
            </Button>
          </div>
        </div>
      </CardHeader>

      {/* Table */}
      <CardBody className="flex-1 overflow-auto p-0">
        <Table
          aria-label="Monthly team planning table"
          className="h-full"
          removeWrapper
          isHeaderSticky
        >
          <TableHeader>
            {columns.map((column) => (
              <TableColumn key={column.key} className="bg-slate-50">
                <div className="flex items-center gap-1">
                  <span className="font-semibold">{column.label}</span>
                  <Tooltip content={column.description}>
                    <InformationCircleIcon className="w-4 h-4 text-slate-400 cursor-help" />
                  </Tooltip>
                </div>
              </TableColumn>
            ))}
          </TableHeader>
          <TableBody>
            {data.map((row, rowIndex) => (
              <TableRow key={row.month} className="hover:bg-slate-50/50">
                {columns.map((column) => (
                  <TableCell key={column.key}>
                    {column.key === 'month' ? (
                      <Chip size="sm" variant="flat" color="default">
                        {row[column.key]}
                      </Chip>
                    ) : column.key === 'totalBurn' ? (
                      <Chip
                        size="sm"
                        color={getBurnColor(row.totalBurn)}
                        variant="flat"
                      >
                        {formatCurrency(row.totalBurn)}
                      </Chip>
                    ) : column.key === 'eventsCost' ? (
                      <Input
                        type="number"
                        value={row[column.key].toString()}
                        onChange={(e) => updateCell(rowIndex, column.key as keyof MonthData, e.target.value)}
                        size="sm"
                        variant="bordered"
                        min="0"
                        startContent={<span className="text-xs text-slate-500">$</span>}
                        className="w-24"
                      />
                    ) : (
                      <div className="flex items-center gap-1">
                        <Button
                          size="sm"
                          variant="flat"
                          color="default"
                          isIconOnly
                          onClick={() => adjustTeamSize(rowIndex, column.key as keyof MonthData, -1)}
                          className="min-w-6 w-6 h-6"
                        >
                          <MinusIcon className="w-3 h-3" />
                        </Button>
                        <Input
                          type="number"
                          value={row[column.key as keyof MonthData].toString()}
                          onChange={(e) => updateCell(rowIndex, column.key as keyof MonthData, e.target.value)}
                          size="sm"
                          variant="bordered"
                          min="0"
                          className="w-16 text-center"
                        />
                        <Button
                          size="sm"
                          variant="flat"
                          color="default"
                          isIconOnly
                          onClick={() => adjustTeamSize(rowIndex, column.key as keyof MonthData, 1)}
                          className="min-w-6 w-6 h-6"
                        >
                          <PlusIcon className="w-3 h-3" />
                        </Button>
                      </div>
                    )}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardBody>
    </div>
  );
}
