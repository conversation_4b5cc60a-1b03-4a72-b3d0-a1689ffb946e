<Window x:Class="Planna.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Planna"
        xmlns:oxy="http://oxyplot.org/wpf"
        mc:Ignorable="d"
        Title="Planna - Growth Planning Tool" 
        Height="900" Width="1400" 
        MinHeight="700" MinWidth="1000"
        WindowStartupLocation="CenterScreen"
        Background="{StaticResource BackgroundBrush}">
    
    <Grid>
        <Grid.RowDefinitions>
            <!-- Header -->
            <RowDefinition Height="Auto"/>
            <!-- Business Assumptions (Collapsible) -->
            <RowDefinition Height="Auto"/>
            <!-- Main Content Area -->
            <RowDefinition Height="2*"/>
            <!-- Monthly Planning Table -->
            <RowDefinition Height="1*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{StaticResource AccentBrush}" Padding="20,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Ellipse Width="40" Height="40" Fill="{StaticResource AccentLightBrush}" Margin="0,0,15,0">
                        <Ellipse.Effect>
                            <DropShadowEffect BlurRadius="10" ShadowDepth="2" Opacity="0.3"/>
                        </Ellipse.Effect>
                    </Ellipse>
                    <StackPanel VerticalAlignment="Center">
                        <TextBlock Text="Planna" FontSize="24" FontWeight="Bold" Foreground="White"/>
                        <TextBlock Text="Growth Planning Tool" FontSize="12" Foreground="White" Opacity="0.8"/>
                    </StackPanel>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button Content="Help" Margin="0,0,10,0" Background="White" Foreground="{StaticResource AccentBrush}" Padding="15,8" FontWeight="SemiBold"/>
                    <Button Content="Export" Margin="0,0,10,0" Background="Transparent" Foreground="White" BorderBrush="White" Padding="15,8"/>
                    <Button Content="Save Scenario" Background="White" Foreground="{StaticResource AccentBrush}" Padding="15,8" FontWeight="SemiBold"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Business Assumptions Panel -->
        <Expander Grid.Row="1" Header="Business Assumptions - Configure Global Settings" IsExpanded="False" Margin="20,10" 
                  Background="White" BorderBrush="#E1DFDD" BorderThickness="1">
            <Grid Margin="20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <!-- Revenue Settings -->
                <StackPanel Grid.Column="0" Margin="0,0,15,0">
                    <TextBlock Text="Revenue Settings" FontWeight="SemiBold" FontSize="14" Margin="0,0,0,10"/>
                    <TextBlock Text="Pricing Tiers" FontWeight="Medium" Margin="0,0,0,5"/>
                    
                    <StackPanel>
                        <TextBlock Text="Basic Plan ($)" FontSize="11" Margin="0,5,0,2"/>
                        <TextBox Text="{Binding BasicPlan, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                        <TextBlock Text="Standard Plan ($)" FontSize="11" Margin="0,5,0,2"/>
                        <TextBox Text="{Binding StandardPlan, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                        <TextBlock Text="Professional Plan ($)" FontSize="11" Margin="0,5,0,2"/>
                        <TextBox Text="{Binding ProfessionalPlan, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                        <TextBlock Text="Enterprise Plan ($)" FontSize="11" Margin="0,5,0,2"/>
                        <TextBox Text="{Binding EnterprisePlan, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                    </StackPanel>

                    <Border Background="{StaticResource AccentBrush}" CornerRadius="5" Padding="10" Margin="0,10">
                        <TextBlock Foreground="White" FontWeight="SemiBold" HorizontalAlignment="Center">
                            <TextBlock.Text>
                                <MultiBinding StringFormat="Blended ARPU: ${0:F0}">
                                    <Binding Path="BlendedARPU"/>
                                </MultiBinding>
                            </TextBlock.Text>
                        </TextBlock>
                    </Border>
                </StackPanel>
                
                <!-- Customer Mix -->
                <StackPanel Grid.Column="1" Margin="0,0,15,0">
                    <TextBlock Text="Customer Mix" FontWeight="SemiBold" FontSize="14" Margin="0,0,0,10"/>

                    <StackPanel>
                        <!-- Basic Plan Mix -->
                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Basic" FontSize="11" VerticalAlignment="Center" Width="60"/>
                            <Slider Grid.Column="1" Value="{Binding BasicMixPercent}" Minimum="0" Maximum="100"
                                    TickFrequency="5" IsSnapToTickEnabled="True" Margin="5,0"/>
                            <TextBox Grid.Column="2" Text="{Binding BasicMixPercent, UpdateSourceTrigger=PropertyChanged}"
                                     Width="40" FontSize="11" HorizontalContentAlignment="Center"/>
                        </Grid>

                        <!-- Standard Plan Mix -->
                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Standard" FontSize="11" VerticalAlignment="Center" Width="60"/>
                            <Slider Grid.Column="1" Value="{Binding StandardMixPercent}" Minimum="0" Maximum="100"
                                    TickFrequency="5" IsSnapToTickEnabled="True" Margin="5,0"/>
                            <TextBox Grid.Column="2" Text="{Binding StandardMixPercent, UpdateSourceTrigger=PropertyChanged}"
                                     Width="40" FontSize="11" HorizontalContentAlignment="Center"/>
                        </Grid>

                        <!-- Professional Plan Mix -->
                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Pro" FontSize="11" VerticalAlignment="Center" Width="60"/>
                            <Slider Grid.Column="1" Value="{Binding ProfessionalMixPercent}" Minimum="0" Maximum="100"
                                    TickFrequency="1" IsSnapToTickEnabled="True" Margin="5,0"/>
                            <TextBox Grid.Column="2" Text="{Binding ProfessionalMixPercent, UpdateSourceTrigger=PropertyChanged}"
                                     Width="40" FontSize="11" HorizontalContentAlignment="Center"/>
                        </Grid>

                        <!-- Enterprise Plan Mix -->
                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Enterprise" FontSize="11" VerticalAlignment="Center" Width="60"/>
                            <Slider Grid.Column="1" Value="{Binding EnterpriseMixPercent}" Minimum="0" Maximum="100"
                                    TickFrequency="1" IsSnapToTickEnabled="True" Margin="5,0"/>
                            <TextBox Grid.Column="2" Text="{Binding EnterpriseMixPercent, UpdateSourceTrigger=PropertyChanged}"
                                     Width="40" FontSize="11" HorizontalContentAlignment="Center"/>
                        </Grid>

                        <!-- Validation Message -->
                        <Border CornerRadius="5" Padding="8" Margin="0,10">
                            <Border.Style>
                                <Style TargetType="Border">
                                    <Setter Property="Background" Value="LightGreen"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsCustomerMixValid}" Value="False">
                                            <Setter Property="Background" Value="LightCoral"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Border.Style>
                            <TextBlock Text="{Binding CustomerMixValidationMessage}" FontSize="11" FontWeight="SemiBold"
                                       HorizontalAlignment="Center" Foreground="White"/>
                        </Border>
                    </StackPanel>
                </StackPanel>
                
                <!-- Team Productivity -->
                <StackPanel Grid.Column="2" Margin="0,0,15,0">
                    <TextBlock Text="Team Productivity" FontWeight="SemiBold" FontSize="14" Margin="0,0,0,10"/>

                    <TextBlock Text="Sales Team" FontWeight="Medium" Margin="0,0,0,5"/>
                    <TextBlock Text="Customers per Rep" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding CustomersPerSalesRep, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                    <TextBlock Text="Ramp Time (months)" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding SalesRampMonths, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,10"/>

                    <TextBlock Text="Marketing Team" FontWeight="Medium" Margin="0,0,0,5"/>
                    <TextBlock Text="Leads per Marketer" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding LeadsPerMarketer, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                    <TextBlock Text="Lead Conversion %" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding LeadConversionPercent, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                </StackPanel>
                
                <!-- Salaries & Costs -->
                <StackPanel Grid.Column="3">
                    <TextBlock Text="Monthly Salaries" FontWeight="SemiBold" FontSize="14" Margin="0,0,0,10"/>

                    <TextBlock Text="Sales Rep ($)" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding SalesRepSalary, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                    <TextBlock Text="Marketing ($)" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding MarketingSalary, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                    <TextBlock Text="Developer ($)" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding DeveloperSalary, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                    <TextBlock Text="Operations ($)" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding OperationsSalary, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                    <TextBlock Text="Leadership ($)" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding LeadershipSalary, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,10"/>

                    <TextBlock Text="Fixed Monthly Costs ($)" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding FixedMonthlyCosts, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                </StackPanel>
            </Grid>
        </Expander>

        <!-- Main Content Area: Chart + Unit Economics -->
        <Grid Grid.Row="2" Margin="20,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="1*"/>
            </Grid.ColumnDefinitions>
            
            <!-- Chart Area -->
            <Border Grid.Column="0" Background="White" BorderBrush="#E1DFDD" BorderThickness="1" Margin="0,0,10,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Chart Header -->
                    <Border Grid.Row="0" Background="{StaticResource AccentBrush}" Padding="15,10">
                        <StackPanel Orientation="Horizontal">
                            <Ellipse Width="24" Height="24" Fill="White" Margin="0,0,10,0"/>
                            <StackPanel>
                                <TextBlock Text="Growth Visualization" FontWeight="SemiBold" Foreground="White"/>
                                <TextBlock Text="Track key metrics over time" FontSize="11" Foreground="White" Opacity="0.8"/>
                            </StackPanel>
                        </StackPanel>
                    </Border>
                    
                    <!-- Metric Selection -->
                    <WrapPanel Grid.Row="1" Margin="15,10" Orientation="Horizontal">
                        <ToggleButton x:Name="MRRToggle" Content="MRR" IsChecked="True" Margin="0,0,5,5" Padding="8,4"
                                      Checked="MetricToggle_Changed" Unchecked="MetricToggle_Changed"/>
                        <ToggleButton x:Name="CustomersToggle" Content="Customers" IsChecked="True" Margin="0,0,5,5" Padding="8,4"
                                      Checked="MetricToggle_Changed" Unchecked="MetricToggle_Changed"/>
                        <ToggleButton x:Name="CashToggle" Content="Cash on Hand" Margin="0,0,5,5" Padding="8,4"
                                      Checked="MetricToggle_Changed" Unchecked="MetricToggle_Changed"/>
                        <ToggleButton x:Name="BurnToggle" Content="Monthly Burn" Margin="0,0,5,5" Padding="8,4"
                                      Checked="MetricToggle_Changed" Unchecked="MetricToggle_Changed"/>
                        <ToggleButton x:Name="TeamToggle" Content="Team Size" Margin="0,0,5,5" Padding="8,4"
                                      Checked="MetricToggle_Changed" Unchecked="MetricToggle_Changed"/>
                        <ToggleButton x:Name="RunwayToggle" Content="Runway" Margin="0,0,5,5" Padding="8,4"
                                      Checked="MetricToggle_Changed" Unchecked="MetricToggle_Changed"/>
                    </WrapPanel>
                    
                    <!-- Chart -->
                    <oxy:PlotView Grid.Row="2" Margin="15" x:Name="GrowthChart"/>
                </Grid>
            </Border>
            
            <!-- Unit Economics Panel -->
            <Border Grid.Column="1" Background="White" BorderBrush="#E1DFDD" BorderThickness="1">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Unit Economics Header -->
                    <Border Grid.Row="0" Background="{StaticResource AccentBrush}" Padding="15,10">
                        <StackPanel Orientation="Horizontal">
                            <Ellipse Width="24" Height="24" Fill="White" Margin="0,0,10,0"/>
                            <StackPanel>
                                <TextBlock Text="Unit Economics" FontWeight="SemiBold" Foreground="White"/>
                                <TextBlock Text="Feb 2026" FontSize="11" Foreground="White" Opacity="0.8"/>
                            </StackPanel>
                        </StackPanel>
                    </Border>
                    
                    <!-- Investor-Grade Metrics -->
                    <ScrollViewer Grid.Row="1" Padding="15">
                        <StackPanel DataContext="{Binding SelectedMonthProjection, RelativeSource={RelativeSource AncestorType=Window}}">

                            <!-- Key Revenue Metrics -->
                            <Border Background="#F8F9FA" BorderBrush="#E1DFDD" BorderThickness="1" CornerRadius="5" Padding="15" Margin="0,0,0,10">
                                <StackPanel>
                                    <TextBlock Text="📊 Revenue Metrics" FontWeight="SemiBold" FontSize="14" Margin="0,0,0,10"/>

                                    <Grid Margin="0,0,0,10">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <Border Grid.Column="0" Background="{StaticResource AccentBrush}"
                                                CornerRadius="5" Padding="8" Margin="0,0,3,0">
                                            <StackPanel HorizontalAlignment="Center">
                                                <TextBlock Text="Total Customers" FontSize="9" Foreground="White" HorizontalAlignment="Center"/>
                                                <TextBlock Text="{Binding TotalCustomers, StringFormat=N0}" FontSize="16" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                                            </StackPanel>
                                        </Border>

                                        <Border Grid.Column="1" Background="Green"
                                                CornerRadius="5" Padding="8" Margin="3,0,0,0">
                                            <StackPanel HorizontalAlignment="Center">
                                                <TextBlock Text="New This Month" FontSize="9" Foreground="White" HorizontalAlignment="Center"/>
                                                <TextBlock FontSize="16" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center">
                                                    <TextBlock.Text>
                                                        <MultiBinding StringFormat="+{0:N0}">
                                                            <Binding Path="NewCustomers"/>
                                                        </MultiBinding>
                                                    </TextBlock.Text>
                                                </TextBlock>
                                            </StackPanel>
                                        </Border>
                                    </Grid>

                                    <StackPanel>
                                        <Grid Margin="0,0,0,3">
                                            <TextBlock Text="MRR" FontSize="11"/>
                                            <TextBlock Text="{Binding MRR, StringFormat=C0}" FontSize="11" FontWeight="Bold" Foreground="{StaticResource AccentBrush}" HorizontalAlignment="Right"/>
                                        </Grid>
                                        <Grid Margin="0,0,0,3">
                                            <TextBlock Text="ARR" FontSize="11"/>
                                            <TextBlock Text="{Binding ARR, StringFormat=C0}" FontSize="11" FontWeight="Bold" Foreground="Purple" HorizontalAlignment="Right"/>
                                        </Grid>
                                        <Grid>
                                            <TextBlock Text="Growth Rate" FontSize="11"/>
                                            <TextBlock Text="{Binding MonthlyGrowthRate, StringFormat='{}{0:F1}%'}" FontSize="11" FontWeight="Bold" HorizontalAlignment="Right">
                                                <TextBlock.Foreground>
                                                    <SolidColorBrush Color="{Binding GrowthHealth, Converter={StaticResource HealthToColorConverter}}"/>
                                                </TextBlock.Foreground>
                                            </TextBlock>
                                        </Grid>
                                    </StackPanel>
                                </StackPanel>
                            </Border>

                            <!-- LTV:CAC Ratio - Key Investor Metric -->
                            <Border BorderBrush="#E1DFDD" BorderThickness="1" CornerRadius="5" Padding="15" Margin="0,0,0,10">
                                <Border.Background>
                                    <SolidColorBrush Color="{Binding LTVCACHealth, Converter={StaticResource HealthToColorConverter}}" Opacity="0.1"/>
                                </Border.Background>
                                <StackPanel>
                                    <Grid Margin="0,0,0,10">
                                        <TextBlock Text="🎯 LTV:CAC Ratio" FontWeight="SemiBold" FontSize="14"/>
                                        <TextBlock Text="{Binding LTVCACHealth, Converter={StaticResource HealthToIconConverter}}" FontSize="16" HorizontalAlignment="Right"/>
                                    </Grid>

                                    <Grid Margin="0,0,0,10">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <StackPanel Grid.Column="0">
                                            <TextBlock Text="{Binding LTVToCACRatio, StringFormat='{}{0:F1}:1'}" FontSize="24" FontWeight="Bold">
                                                <TextBlock.Foreground>
                                                    <SolidColorBrush Color="{Binding LTVCACHealth, Converter={StaticResource HealthToColorConverter}}"/>
                                                </TextBlock.Foreground>
                                            </TextBlock>
                                            <TextBlock Text="{Binding LTVCACHealth, Converter={StaticResource HealthToMessageConverter}, ConverterParameter='LTV:CAC'}"
                                                       FontSize="10" TextWrapping="Wrap" Margin="0,5"/>
                                        </StackPanel>

                                        <StackPanel Grid.Column="1" HorizontalAlignment="Right">
                                            <TextBlock Text="Benchmarks:" FontSize="10" FontWeight="SemiBold"/>
                                            <TextBlock Text="🟢 Excellent: 5:1+" FontSize="9"/>
                                            <TextBlock Text="🟡 Good: 3:1+" FontSize="9"/>
                                            <TextBlock Text="🟠 OK: 2:1+" FontSize="9"/>
                                            <TextBlock Text="🔴 Poor: &lt;2:1" FontSize="9"/>
                                        </StackPanel>
                                    </Grid>

                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <StackPanel Grid.Column="0">
                                            <TextBlock Text="LTV" FontSize="10" Foreground="Gray"/>
                                            <TextBlock Text="{Binding LTV, StringFormat=C0}" FontSize="12" FontWeight="SemiBold"/>
                                        </StackPanel>
                                        <StackPanel Grid.Column="1">
                                            <TextBlock Text="CAC" FontSize="10" Foreground="Gray"/>
                                            <TextBlock Text="{Binding CAC, StringFormat=C0}" FontSize="12" FontWeight="SemiBold"/>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </Border>

                            <!-- Payback Period -->
                            <Border BorderBrush="#E1DFDD" BorderThickness="1" CornerRadius="5" Padding="15" Margin="0,0,0,10">
                                <Border.Background>
                                    <SolidColorBrush Color="{Binding PaybackHealth, Converter={StaticResource HealthToColorConverter}}" Opacity="0.1"/>
                                </Border.Background>
                                <StackPanel>
                                    <Grid Margin="0,0,0,5">
                                        <TextBlock Text="⏱️ Payback Period" FontWeight="SemiBold" FontSize="14"/>
                                        <TextBlock Text="{Binding PaybackHealth, Converter={StaticResource HealthToIconConverter}}" FontSize="16" HorizontalAlignment="Right"/>
                                    </Grid>

                                    <TextBlock Text="{Binding PaybackPeriodMonths, StringFormat='{}{0:F1} months'}" FontSize="20" FontWeight="Bold">
                                        <TextBlock.Foreground>
                                            <SolidColorBrush Color="{Binding PaybackHealth, Converter={StaticResource HealthToColorConverter}}"/>
                                        </TextBlock.Foreground>
                                    </TextBlock>
                                    <TextBlock Text="{Binding PaybackHealth, Converter={StaticResource HealthToMessageConverter}, ConverterParameter='Payback'}"
                                               FontSize="10" TextWrapping="Wrap" Margin="0,5"/>
                                </StackPanel>
                            </Border>

                            <!-- Financial Health -->
                            <Border BorderBrush="#E1DFDD" BorderThickness="1" CornerRadius="5" Padding="15">
                                <Border.Background>
                                    <SolidColorBrush Color="{Binding RunwayHealth, Converter={StaticResource HealthToColorConverter}}" Opacity="0.1"/>
                                </Border.Background>
                                <StackPanel>
                                    <TextBlock Text="💰 Financial Health" FontWeight="SemiBold" FontSize="14" Margin="0,0,0,10"/>

                                    <Grid Margin="0,0,0,10">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <Border Grid.Column="0" Background="{StaticResource AccentBrush}"
                                                CornerRadius="5" Padding="8" Margin="0,0,3,0">
                                            <StackPanel HorizontalAlignment="Center">
                                                <TextBlock Text="Cash on Hand" FontSize="9" Foreground="White" HorizontalAlignment="Center"/>
                                                <TextBlock Text="{Binding CashOnHand, StringFormat=C0}" FontSize="14" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                                            </StackPanel>
                                        </Border>

                                        <Border Grid.Column="1" Background="Red"
                                                CornerRadius="5" Padding="8" Margin="3,0,0,0">
                                            <StackPanel HorizontalAlignment="Center">
                                                <TextBlock Text="Monthly Burn" FontSize="9" Foreground="White" HorizontalAlignment="Center"/>
                                                <TextBlock Text="{Binding MonthlyBurn, StringFormat=C0}" FontSize="14" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                                            </StackPanel>
                                        </Border>
                                    </Grid>

                                    <Border CornerRadius="5" Padding="10" Margin="0,5">
                                        <Border.Background>
                                            <SolidColorBrush Color="{Binding RunwayHealth, Converter={StaticResource HealthToColorConverter}}"/>
                                        </Border.Background>
                                        <StackPanel>
                                            <Grid>
                                                <TextBlock Text="Runway" FontSize="12" Foreground="White"/>
                                                <TextBlock Text="{Binding RunwayHealth, Converter={StaticResource HealthToIconConverter}}" FontSize="14" Foreground="White" HorizontalAlignment="Right"/>
                                            </Grid>
                                            <TextBlock Text="{Binding RunwayMonths, StringFormat='{}{0:F1} months'}" FontSize="16" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                                            <ProgressBar Value="{Binding RunwayMonths}" Maximum="24" Height="6" Margin="0,5"/>
                                        </StackPanel>
                                    </Border>
                                </StackPanel>
                            </Border>

                        </StackPanel>
                    </ScrollViewer>
                </Grid>
            </Border>
        </Grid>

        <!-- Monthly Planning Table -->
        <Border Grid.Row="3" Background="White" BorderBrush="#E1DFDD" BorderThickness="1" Margin="20,10,20,20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <!-- Table Header -->
                <Border Grid.Row="0" Background="{StaticResource AccentBrush}" Padding="15,10">
                    <Grid>
                        <StackPanel Orientation="Horizontal">
                            <Ellipse Width="24" Height="24" Fill="White" Margin="0,0,10,0"/>
                            <StackPanel>
                                <TextBlock Text="Monthly Team Planning" FontWeight="SemiBold" Foreground="White"/>
                                <TextBlock Text="Plan team growth and expenses month by month" FontSize="11" Foreground="White" Opacity="0.8"/>
                            </StackPanel>
                        </StackPanel>
                        
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                            <Border Background="White" CornerRadius="10" Padding="8,2" Margin="0,0,10,0" Opacity="0.9">
                                <TextBlock Text="10 months planned" FontSize="10" Foreground="{StaticResource AccentBrush}"/>
                            </Border>
                            <Button Content="Add Month" Background="White" Foreground="{StaticResource AccentBrush}" Padding="10,5" FontWeight="SemiBold"/>
                        </StackPanel>
                    </Grid>
                </Border>
                
                <!-- Data Grid -->
                <DataGrid Grid.Row="1" x:Name="MonthlyPlanningGrid" 
                          AutoGenerateColumns="False" 
                          CanUserAddRows="False" 
                          CanUserDeleteRows="False"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          Margin="15">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Month" Binding="{Binding Month}" IsReadOnly="True" Width="100"/>

                        <DataGridTemplateColumn Header="Sales Reps" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <local:TeamSizeEditor Value="{Binding SalesReps, UpdateSourceTrigger=PropertyChanged}"
                                                          Minimum="0" Maximum="50"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTemplateColumn Header="Marketing" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <local:TeamSizeEditor Value="{Binding MarketingTeam, UpdateSourceTrigger=PropertyChanged}"
                                                          Minimum="0" Maximum="50"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTemplateColumn Header="Development" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <local:TeamSizeEditor Value="{Binding DevTeam, UpdateSourceTrigger=PropertyChanged}"
                                                          Minimum="0" Maximum="50"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTemplateColumn Header="Operations" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <local:TeamSizeEditor Value="{Binding OpsTeam, UpdateSourceTrigger=PropertyChanged}"
                                                          Minimum="0" Maximum="50"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTemplateColumn Header="Leadership" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <local:TeamSizeEditor Value="{Binding LeadershipTeam, UpdateSourceTrigger=PropertyChanged}"
                                                          Minimum="0" Maximum="20"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTextColumn Header="Events Budget" Binding="{Binding EventsCost, StringFormat=C0}" Width="100"/>
                        <DataGridTextColumn Header="Total Monthly Burn" Binding="{Binding TotalBurn, StringFormat=C0}" IsReadOnly="True" Width="130">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontWeight" Value="SemiBold"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding BurnLevel}" Value="High">
                                            <Setter Property="Foreground" Value="Red"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding BurnLevel}" Value="Medium">
                                            <Setter Property="Foreground" Value="Orange"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding BurnLevel}" Value="Low">
                                            <Setter Property="Foreground" Value="Green"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>

    </Grid>
</Window>
