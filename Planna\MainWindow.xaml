<Window x:Class="Planna.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Planna"
        xmlns:oxy="http://oxyplot.org/wpf"
        mc:Ignorable="d"
        Title="Planna - Growth Planning Tool" 
        Height="900" Width="1400" 
        MinHeight="700" MinWidth="1000"
        WindowStartupLocation="CenterScreen"
        Background="{StaticResource BackgroundBrush}">
    
    <Grid>
        <Grid.RowDefinitions>
            <!-- Header -->
            <RowDefinition Height="Auto"/>
            <!-- Business Assumptions (Collapsible) -->
            <RowDefinition Height="Auto"/>
            <!-- Main Content Area -->
            <RowDefinition Height="2*"/>
            <!-- Monthly Planning Table -->
            <RowDefinition Height="1*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{StaticResource AccentBrush}" Padding="20,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Ellipse Width="40" Height="40" Fill="{StaticResource AccentLightBrush}" Margin="0,0,15,0">
                        <Ellipse.Effect>
                            <DropShadowEffect BlurRadius="10" ShadowDepth="2" Opacity="0.3"/>
                        </Ellipse.Effect>
                    </Ellipse>
                    <StackPanel VerticalAlignment="Center">
                        <TextBlock Text="Planna" FontSize="24" FontWeight="Bold" Foreground="White"/>
                        <TextBlock Text="Growth Planning Tool" FontSize="12" Foreground="White" Opacity="0.8"/>
                    </StackPanel>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button Content="Help" Margin="0,0,10,0" Background="White" Foreground="{StaticResource AccentBrush}" Padding="15,8" FontWeight="SemiBold"/>
                    <Button Content="Export" Margin="0,0,10,0" Background="Transparent" Foreground="White" BorderBrush="White" Padding="15,8"/>
                    <Button Content="Save Scenario" Background="White" Foreground="{StaticResource AccentBrush}" Padding="15,8" FontWeight="SemiBold"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Business Assumptions Panel -->
        <Expander Grid.Row="1" Header="Business Assumptions - Configure Global Settings" IsExpanded="False" Margin="20,10" 
                  Background="White" BorderBrush="#E1DFDD" BorderThickness="1">
            <Grid Margin="20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <!-- Revenue Settings -->
                <StackPanel Grid.Column="0" Margin="0,0,15,0">
                    <TextBlock Text="Revenue Settings" FontWeight="SemiBold" FontSize="14" Margin="0,0,0,10"/>
                    <TextBlock Text="Pricing Tiers" FontWeight="Medium" Margin="0,0,0,5"/>
                    
                    <StackPanel>
                        <TextBlock Text="Basic Plan ($)" FontSize="11" Margin="0,5,0,2"/>
                        <TextBox Text="{Binding BasicPlan, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                        <TextBlock Text="Standard Plan ($)" FontSize="11" Margin="0,5,0,2"/>
                        <TextBox Text="{Binding StandardPlan, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                        <TextBlock Text="Professional Plan ($)" FontSize="11" Margin="0,5,0,2"/>
                        <TextBox Text="{Binding ProfessionalPlan, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                        <TextBlock Text="Enterprise Plan ($)" FontSize="11" Margin="0,5,0,2"/>
                        <TextBox Text="{Binding EnterprisePlan, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                    </StackPanel>

                    <Border Background="{StaticResource AccentBrush}" CornerRadius="5" Padding="10" Margin="0,10">
                        <TextBlock Foreground="White" FontWeight="SemiBold" HorizontalAlignment="Center">
                            <TextBlock.Text>
                                <MultiBinding StringFormat="Blended ARPU: ${0:F0}">
                                    <Binding Path="BlendedARPU"/>
                                </MultiBinding>
                            </TextBlock.Text>
                        </TextBlock>
                    </Border>
                </StackPanel>
                
                <!-- Customer Mix -->
                <StackPanel Grid.Column="1" Margin="0,0,15,0">
                    <TextBlock Text="Customer Mix" FontWeight="SemiBold" FontSize="14" Margin="0,0,0,10"/>

                    <StackPanel>
                        <!-- Basic Plan Mix -->
                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Basic" FontSize="11" VerticalAlignment="Center" Width="60"/>
                            <Slider Grid.Column="1" Value="{Binding BasicMixPercent}" Minimum="0" Maximum="100"
                                    TickFrequency="5" IsSnapToTickEnabled="True" Margin="5,0"/>
                            <TextBox Grid.Column="2" Text="{Binding BasicMixPercent, UpdateSourceTrigger=PropertyChanged}"
                                     Width="40" FontSize="11" HorizontalContentAlignment="Center"/>
                        </Grid>

                        <!-- Standard Plan Mix -->
                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Standard" FontSize="11" VerticalAlignment="Center" Width="60"/>
                            <Slider Grid.Column="1" Value="{Binding StandardMixPercent}" Minimum="0" Maximum="100"
                                    TickFrequency="5" IsSnapToTickEnabled="True" Margin="5,0"/>
                            <TextBox Grid.Column="2" Text="{Binding StandardMixPercent, UpdateSourceTrigger=PropertyChanged}"
                                     Width="40" FontSize="11" HorizontalContentAlignment="Center"/>
                        </Grid>

                        <!-- Professional Plan Mix -->
                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Pro" FontSize="11" VerticalAlignment="Center" Width="60"/>
                            <Slider Grid.Column="1" Value="{Binding ProfessionalMixPercent}" Minimum="0" Maximum="100"
                                    TickFrequency="1" IsSnapToTickEnabled="True" Margin="5,0"/>
                            <TextBox Grid.Column="2" Text="{Binding ProfessionalMixPercent, UpdateSourceTrigger=PropertyChanged}"
                                     Width="40" FontSize="11" HorizontalContentAlignment="Center"/>
                        </Grid>

                        <!-- Enterprise Plan Mix -->
                        <Grid Margin="0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Enterprise" FontSize="11" VerticalAlignment="Center" Width="60"/>
                            <Slider Grid.Column="1" Value="{Binding EnterpriseMixPercent}" Minimum="0" Maximum="100"
                                    TickFrequency="1" IsSnapToTickEnabled="True" Margin="5,0"/>
                            <TextBox Grid.Column="2" Text="{Binding EnterpriseMixPercent, UpdateSourceTrigger=PropertyChanged}"
                                     Width="40" FontSize="11" HorizontalContentAlignment="Center"/>
                        </Grid>

                        <!-- Validation Message -->
                        <Border CornerRadius="5" Padding="8" Margin="0,10">
                            <Border.Style>
                                <Style TargetType="Border">
                                    <Setter Property="Background" Value="LightGreen"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsCustomerMixValid}" Value="False">
                                            <Setter Property="Background" Value="LightCoral"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Border.Style>
                            <TextBlock Text="{Binding CustomerMixValidationMessage}" FontSize="11" FontWeight="SemiBold"
                                       HorizontalAlignment="Center" Foreground="White"/>
                        </Border>
                    </StackPanel>
                </StackPanel>
                
                <!-- Team Productivity -->
                <StackPanel Grid.Column="2" Margin="0,0,15,0">
                    <TextBlock Text="Team Productivity" FontWeight="SemiBold" FontSize="14" Margin="0,0,0,10"/>

                    <TextBlock Text="Sales Team" FontWeight="Medium" Margin="0,0,0,5"/>
                    <TextBlock Text="Customers per Rep" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding CustomersPerSalesRep, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                    <TextBlock Text="Ramp Time (months)" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding SalesRampMonths, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,10"/>

                    <TextBlock Text="Marketing Team" FontWeight="Medium" Margin="0,0,0,5"/>
                    <TextBlock Text="Leads per Marketer" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding LeadsPerMarketer, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                    <TextBlock Text="Lead Conversion %" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding LeadConversionPercent, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                </StackPanel>
                
                <!-- Salaries & Costs -->
                <StackPanel Grid.Column="3">
                    <TextBlock Text="Monthly Salaries" FontWeight="SemiBold" FontSize="14" Margin="0,0,0,10"/>

                    <TextBlock Text="Sales Rep ($)" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding SalesRepSalary, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                    <TextBlock Text="Marketing ($)" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding MarketingSalary, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                    <TextBlock Text="Developer ($)" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding DeveloperSalary, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                    <TextBlock Text="Operations ($)" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding OperationsSalary, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                    <TextBlock Text="Leadership ($)" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding LeadershipSalary, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,10"/>

                    <TextBlock Text="Fixed Monthly Costs ($)" FontSize="11" Margin="0,5,0,2"/>
                    <TextBox Text="{Binding FixedMonthlyCosts, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,5"/>
                </StackPanel>
            </Grid>
        </Expander>

        <!-- Main Content Area: Chart + Unit Economics -->
        <Grid Grid.Row="2" Margin="20,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="1*"/>
            </Grid.ColumnDefinitions>
            
            <!-- Chart Area -->
            <Border Grid.Column="0" Background="White" BorderBrush="#E1DFDD" BorderThickness="1" Margin="0,0,10,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Chart Header -->
                    <Border Grid.Row="0" Background="{StaticResource AccentBrush}" Padding="15,10">
                        <StackPanel Orientation="Horizontal">
                            <Ellipse Width="24" Height="24" Fill="White" Margin="0,0,10,0"/>
                            <StackPanel>
                                <TextBlock Text="Growth Visualization" FontWeight="SemiBold" Foreground="White"/>
                                <TextBlock Text="Track key metrics over time" FontSize="11" Foreground="White" Opacity="0.8"/>
                            </StackPanel>
                        </StackPanel>
                    </Border>
                    
                    <!-- Metric Selection -->
                    <WrapPanel Grid.Row="1" Margin="15,10" Orientation="Horizontal">
                        <ToggleButton Content="MRR" IsChecked="True" Margin="0,0,5,5" Padding="8,4"/>
                        <ToggleButton Content="Customers" IsChecked="True" Margin="0,0,5,5" Padding="8,4"/>
                        <ToggleButton Content="Cash on Hand" Margin="0,0,5,5" Padding="8,4"/>
                        <ToggleButton Content="Monthly Burn" Margin="0,0,5,5" Padding="8,4"/>
                        <ToggleButton Content="Team Size" Margin="0,0,5,5" Padding="8,4"/>
                        <ToggleButton Content="Runway" Margin="0,0,5,5" Padding="8,4"/>
                    </WrapPanel>
                    
                    <!-- Chart -->
                    <oxy:PlotView Grid.Row="2" Margin="15" x:Name="GrowthChart"/>
                </Grid>
            </Border>
            
            <!-- Unit Economics Panel -->
            <Border Grid.Column="1" Background="White" BorderBrush="#E1DFDD" BorderThickness="1">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Unit Economics Header -->
                    <Border Grid.Row="0" Background="{StaticResource AccentBrush}" Padding="15,10">
                        <StackPanel Orientation="Horizontal">
                            <Ellipse Width="24" Height="24" Fill="White" Margin="0,0,10,0"/>
                            <StackPanel>
                                <TextBlock Text="Unit Economics" FontWeight="SemiBold" Foreground="White"/>
                                <TextBlock Text="Feb 2026" FontSize="11" Foreground="White" Opacity="0.8"/>
                            </StackPanel>
                        </StackPanel>
                    </Border>
                    
                    <!-- Metrics -->
                    <ScrollViewer Grid.Row="1" Padding="15">
                        <StackPanel>
                            
                            <!-- Customer Metrics -->
                            <Border Background="#F8F9FA" BorderBrush="#E1DFDD" BorderThickness="1" CornerRadius="5" Padding="15" Margin="0,0,0,15">
                                <StackPanel>
                                    <TextBlock Text="Customer Metrics" FontWeight="SemiBold" FontSize="14" Margin="0,0,0,10"/>
                                    
                                    <Grid Margin="0,0,0,10">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <Border Grid.Column="0" Background="{StaticResource AccentBrush}" 
                                                CornerRadius="5" Padding="10" Margin="0,0,5,0">
                                            <StackPanel HorizontalAlignment="Center">
                                                <TextBlock Text="Total Customers" FontSize="10" Foreground="White" HorizontalAlignment="Center"/>
                                                <TextBlock Text="578" FontSize="18" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                                            </StackPanel>
                                        </Border>
                                        
                                        <Border Grid.Column="1" Background="Green" 
                                                CornerRadius="5" Padding="10" Margin="5,0,0,0">
                                            <StackPanel HorizontalAlignment="Center">
                                                <TextBlock Text="New This Month" FontSize="10" Foreground="White" HorizontalAlignment="Center"/>
                                                <TextBlock Text="+164" FontSize="18" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                                            </StackPanel>
                                        </Border>
                                    </Grid>
                                    
                                    <StackPanel>
                                        <Grid Margin="0,0,0,5">
                                            <TextBlock Text="Blended ARPU" FontSize="12"/>
                                            <TextBlock Text="$83" FontSize="12" FontWeight="SemiBold" HorizontalAlignment="Right"/>
                                        </Grid>
                                        <Grid Margin="0,0,0,5">
                                            <TextBlock Text="Monthly Recurring Revenue" FontSize="12"/>
                                            <TextBlock Text="$48,061" FontSize="12" FontWeight="Bold" Foreground="{StaticResource AccentBrush}" HorizontalAlignment="Right"/>
                                        </Grid>
                                        <Grid>
                                            <TextBlock Text="Annual Recurring Revenue" FontSize="12"/>
                                            <TextBlock Text="$576,732" FontSize="12" FontWeight="Bold" Foreground="Purple" HorizontalAlignment="Right"/>
                                        </Grid>
                                    </StackPanel>
                                </StackPanel>
                            </Border>
                            
                            <!-- Financial Health -->
                            <Border Background="#F8F9FA" BorderBrush="#E1DFDD" BorderThickness="1" CornerRadius="5" Padding="15">
                                <StackPanel>
                                    <TextBlock Text="Financial Health" FontWeight="SemiBold" FontSize="14" Margin="0,0,0,10"/>
                                    
                                    <Grid Margin="0,0,0,10">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <Border Grid.Column="0" Background="{StaticResource AccentBrush}" 
                                                CornerRadius="5" Padding="10" Margin="0,0,5,0">
                                            <StackPanel HorizontalAlignment="Center">
                                                <TextBlock Text="Cash on Hand" FontSize="10" Foreground="White" HorizontalAlignment="Center"/>
                                                <TextBlock Text="$89,425" FontSize="14" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                                            </StackPanel>
                                        </Border>
                                        
                                        <Border Grid.Column="1" Background="Red" 
                                                CornerRadius="5" Padding="10" Margin="5,0,0,0">
                                            <StackPanel HorizontalAlignment="Center">
                                                <TextBlock Text="Monthly Burn" FontSize="10" Foreground="White" HorizontalAlignment="Center"/>
                                                <TextBlock Text="$32,000" FontSize="14" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                                            </StackPanel>
                                        </Border>
                                    </Grid>
                                    
                                    <Border Background="Orange" CornerRadius="5" Padding="10" Margin="0,5">
                                        <StackPanel>
                                            <Grid>
                                                <TextBlock Text="Runway" FontSize="12" Foreground="White"/>
                                                <TextBlock Text="⚠️ Warning" FontSize="10" Foreground="White" HorizontalAlignment="Right"/>
                                            </Grid>
                                            <TextBlock Text="2.8 months" FontSize="16" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                                            <ProgressBar Value="23" Maximum="100" Height="6" Margin="0,5"/>
                                        </StackPanel>
                                    </Border>
                                    
                                    <Grid Margin="0,5">
                                        <TextBlock Text="Break-even Target" FontSize="12"/>
                                        <TextBlock Text="May 2026" FontSize="12" FontWeight="SemiBold" Foreground="Green" HorizontalAlignment="Right"/>
                                    </Grid>
                                </StackPanel>
                            </Border>
                            
                        </StackPanel>
                    </ScrollViewer>
                </Grid>
            </Border>
        </Grid>

        <!-- Monthly Planning Table -->
        <Border Grid.Row="3" Background="White" BorderBrush="#E1DFDD" BorderThickness="1" Margin="20,10,20,20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <!-- Table Header -->
                <Border Grid.Row="0" Background="{StaticResource AccentBrush}" Padding="15,10">
                    <Grid>
                        <StackPanel Orientation="Horizontal">
                            <Ellipse Width="24" Height="24" Fill="White" Margin="0,0,10,0"/>
                            <StackPanel>
                                <TextBlock Text="Monthly Team Planning" FontWeight="SemiBold" Foreground="White"/>
                                <TextBlock Text="Plan team growth and expenses month by month" FontSize="11" Foreground="White" Opacity="0.8"/>
                            </StackPanel>
                        </StackPanel>
                        
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                            <Border Background="White" CornerRadius="10" Padding="8,2" Margin="0,0,10,0" Opacity="0.9">
                                <TextBlock Text="10 months planned" FontSize="10" Foreground="{StaticResource AccentBrush}"/>
                            </Border>
                            <Button Content="Add Month" Background="White" Foreground="{StaticResource AccentBrush}" Padding="10,5" FontWeight="SemiBold"/>
                        </StackPanel>
                    </Grid>
                </Border>
                
                <!-- Data Grid -->
                <DataGrid Grid.Row="1" x:Name="MonthlyPlanningGrid" 
                          AutoGenerateColumns="False" 
                          CanUserAddRows="False" 
                          CanUserDeleteRows="False"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          Margin="15">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Month" Binding="{Binding Month}" IsReadOnly="True" Width="100"/>

                        <DataGridTemplateColumn Header="Sales Reps" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <local:TeamSizeEditor Value="{Binding SalesReps, UpdateSourceTrigger=PropertyChanged}"
                                                          Minimum="0" Maximum="50"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTemplateColumn Header="Marketing" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <local:TeamSizeEditor Value="{Binding MarketingTeam, UpdateSourceTrigger=PropertyChanged}"
                                                          Minimum="0" Maximum="50"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTemplateColumn Header="Development" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <local:TeamSizeEditor Value="{Binding DevTeam, UpdateSourceTrigger=PropertyChanged}"
                                                          Minimum="0" Maximum="50"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTemplateColumn Header="Operations" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <local:TeamSizeEditor Value="{Binding OpsTeam, UpdateSourceTrigger=PropertyChanged}"
                                                          Minimum="0" Maximum="50"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTemplateColumn Header="Leadership" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <local:TeamSizeEditor Value="{Binding LeadershipTeam, UpdateSourceTrigger=PropertyChanged}"
                                                          Minimum="0" Maximum="20"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTextColumn Header="Events Budget" Binding="{Binding EventsCost, StringFormat=C0}" Width="100"/>
                        <DataGridTextColumn Header="Total Monthly Burn" Binding="{Binding TotalBurn, StringFormat=C0}" IsReadOnly="True" Width="130">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontWeight" Value="SemiBold"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding BurnLevel}" Value="High">
                                            <Setter Property="Foreground" Value="Red"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding BurnLevel}" Value="Medium">
                                            <Setter Property="Foreground" Value="Orange"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding BurnLevel}" Value="Low">
                                            <Setter Property="Foreground" Value="Green"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>

    </Grid>
</Window>
