import React, { useState } from "react";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";

// Sample data - this will be replaced with real data from the store
const sampleData = [
  { month: "Aug '25", mrr: 0, customers: 0, cash: 151500, burn: 5000, team: 1 },
  { month: "Sep '25", mrr: 5400, customers: 65, cash: 151900, burn: 11500, team: 3 },
  { month: "Oct '25", mrr: 12000, customers: 145, cash: 152400, burn: 14500, team: 4 },
  { month: "Nov '25", mrr: 19100, customers: 230, cash: 157900, burn: 23000, team: 6 },
  { month: "Dec '25", mrr: 28300, customers: 341, cash: 163200, burn: 32000, team: 8 },
  { month: "Jan '26", mrr: 39600, customers: 477, cash: 171800, burn: 40000, team: 10 },
  { month: "Feb '26", mrr: 48100, customers: 579, cash: 179900, burn: 35000, team: 10 },
];

const metrics = [
  { key: "mrr", label: "MRR", color: "#3b82f6" },
  { key: "customers", label: "Customers", color: "#10b981" },
  { key: "cash", label: "Cash on Hand", color: "#f59e0b" },
  { key: "burn", label: "Monthly Burn", color: "#ef4444" },
  { key: "team", label: "Team Size", color: "#8b5cf6" },
];

export function ChartArea() {
  const [selectedMetrics, setSelectedMetrics] = useState(["mrr", "customers"]);

  const toggleMetric = (metricKey: string) => {
    setSelectedMetrics(prev =>
      prev.includes(metricKey)
        ? prev.filter(m => m !== metricKey)
        : [...prev, metricKey]
    );
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="px-4 py-2 border-b border-slate-200">
        <div className="flex items-center justify-between">
          <h2 className="text-sm font-semibold text-slate-900">Growth Metrics</h2>
          <span className="text-xs text-slate-600">Interactive chart</span>
        </div>
      </div>

      {/* Metric Selection */}
      <div className="px-4 py-2 border-b border-slate-200 bg-slate-50">
        <div className="flex flex-wrap gap-2">
          {metrics.map((metric) => (
            <button
              key={metric.key}
              onClick={() => toggleMetric(metric.key)}
              className={`flex items-center gap-1 px-2 py-1 text-xs font-medium rounded transition-colors ${
                selectedMetrics.includes(metric.key)
                  ? 'bg-white border text-slate-900 shadow-sm'
                  : 'bg-transparent border border-transparent text-slate-600 hover:text-slate-900'
              }`}
              style={{
                borderColor: selectedMetrics.includes(metric.key) ? metric.color : 'transparent'
              }}
            >
              <div
                className="w-2 h-2 rounded-full"
                style={{ backgroundColor: metric.color }}
              />
              {metric.label}
            </button>
          ))}
        </div>
      </div>

      {/* Chart */}
      <div className="flex-1 p-3">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={sampleData}
            margin={{
              top: 20,
              right: 30,
              left: 20,
              bottom: 20,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
            <XAxis
              dataKey="month"
              stroke="#64748b"
              fontSize={12}
              tickLine={false}
              axisLine={false}
            />
            <YAxis
              stroke="#64748b"
              fontSize={12}
              tickLine={false}
              axisLine={false}
              tickFormatter={(value) => {
                if (value >= 1000000) return `${(value / 1000000).toFixed(1)}M`;
                if (value >= 1000) return `${(value / 1000).toFixed(0)}K`;
                return value.toString();
              }}
            />
            <Tooltip
              contentStyle={{
                backgroundColor: "white",
                border: "1px solid #e2e8f0",
                borderRadius: "8px",
                boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
              }}
              labelStyle={{ color: "#1e293b", fontWeight: "600" }}
            />
            <Legend />
            {metrics.map((metric) => {
              if (selectedMetrics.includes(metric.key)) {
                return (
                  <Line
                    key={metric.key}
                    type="monotone"
                    dataKey={metric.key}
                    stroke={metric.color}
                    strokeWidth={3}
                    dot={{ r: 5, fill: metric.color, strokeWidth: 2, stroke: "white" }}
                    activeDot={{ r: 7, fill: metric.color, strokeWidth: 2, stroke: "white" }}
                  />
                );
              }
              return null;
            })}
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}
