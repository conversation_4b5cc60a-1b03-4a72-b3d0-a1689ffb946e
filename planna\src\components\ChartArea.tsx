import React, { useState } from "react";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import {
  Button,
  ButtonGroup,
  Card,
  CardBody,
  CardHeader,
  Chip,
  Tooltip as <PERSON><PERSON><PERSON><PERSON>,
  Divider,
  Switch
} from "@heroui/react";
import {
  ChartBarIcon,
  EyeIcon,
  EyeSlashIcon,
  InformationCircleIcon
} from "@heroicons/react/24/outline";

// Sample data - this will be replaced with real data from the store
const sampleData = [
  { month: "Aug '25", mrr: 0, customers: 0, cash: 151500, burn: 5000, team: 1, runway: 30.3 },
  { month: "Sep '25", mrr: 5400, customers: 65, cash: 151900, burn: 11500, team: 3, runway: 13.2 },
  { month: "Oct '25", mrr: 12000, customers: 145, cash: 152400, burn: 14500, team: 4, runway: 10.5 },
  { month: "Nov '25", mrr: 19100, customers: 230, cash: 157900, burn: 23000, team: 6, runway: 6.9 },
  { month: "Dec '25", mrr: 28300, customers: 341, cash: 163200, burn: 32000, team: 8, runway: 5.1 },
  { month: "Jan '26", mrr: 39600, customers: 477, cash: 171800, burn: 40000, team: 10, runway: 4.3 },
  { month: "Feb '26", mrr: 48100, customers: 579, cash: 179900, burn: 35000, team: 10, runway: 5.1 },
];

const metrics = [
  {
    key: "mrr",
    label: "Monthly Recurring Revenue",
    shortLabel: "MRR",
    color: "#3b82f6",
    format: "currency",
    description: "Total monthly recurring revenue from all customers"
  },
  {
    key: "customers",
    label: "Total Customers",
    shortLabel: "Customers",
    color: "#10b981",
    format: "number",
    description: "Total number of paying customers"
  },
  {
    key: "cash",
    label: "Cash on Hand",
    shortLabel: "Cash",
    color: "#f59e0b",
    format: "currency",
    description: "Available cash reserves"
  },
  {
    key: "burn",
    label: "Monthly Burn Rate",
    shortLabel: "Burn",
    color: "#ef4444",
    format: "currency",
    description: "Total monthly expenses including team and operations"
  },
  {
    key: "team",
    label: "Team Size",
    shortLabel: "Team",
    color: "#8b5cf6",
    format: "number",
    description: "Total number of team members across all departments"
  },
  {
    key: "runway",
    label: "Runway (Months)",
    shortLabel: "Runway",
    color: "#f97316",
    format: "decimal",
    description: "Months of cash remaining at current burn rate"
  },
];

export function ChartArea() {
  const [selectedMetrics, setSelectedMetrics] = useState(["mrr", "customers"]);
  const [showLegend, setShowLegend] = useState(true);

  const toggleMetric = (metricKey: string) => {
    setSelectedMetrics(prev =>
      prev.includes(metricKey)
        ? prev.filter(m => m !== metricKey)
        : [...prev, metricKey]
    );
  };

  const formatTooltipValue = (value: any, format: string) => {
    if (format === "currency") {
      return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(value);
    }
    if (format === "decimal") {
      return `${value} months`;
    }
    return new Intl.NumberFormat("en-US").format(value);
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <CardHeader className="flex flex-row items-center justify-between px-6 py-4">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg">
            <ChartBarIcon className="w-5 h-5 text-white" />
          </div>
          <div>
            <h2 className="text-lg font-semibold text-slate-900">Growth Visualization</h2>
            <p className="text-sm text-slate-500">Track key metrics over time</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <span className="text-sm text-slate-600">Legend</span>
            <Switch
              size="sm"
              isSelected={showLegend}
              onValueChange={setShowLegend}
              color="primary"
            />
          </div>
          <Chip size="sm" variant="flat" color="primary">
            {selectedMetrics.length} metrics selected
          </Chip>
        </div>
      </CardHeader>

      <Divider />

      {/* Metric Selection */}
      <div className="px-6 py-4 bg-slate-50/50">
        <div className="flex items-center gap-3 mb-3">
          <span className="text-sm font-medium text-slate-700">Select Metrics:</span>
          <Button
            size="sm"
            variant="flat"
            color="secondary"
            onClick={() => setSelectedMetrics(metrics.map(m => m.key))}
          >
            Select All
          </Button>
          <Button
            size="sm"
            variant="flat"
            color="default"
            onClick={() => setSelectedMetrics([])}
          >
            Clear All
          </Button>
        </div>
        <div className="flex flex-wrap gap-2">
          {metrics.map((metric) => (
            <HeroTooltip key={metric.key} content={metric.description}>
              <Button
                size="sm"
                variant={selectedMetrics.includes(metric.key) ? "solid" : "bordered"}
                color={selectedMetrics.includes(metric.key) ? "primary" : "default"}
                onClick={() => toggleMetric(metric.key)}
                startContent={
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: metric.color }}
                  />
                }
                endContent={
                  selectedMetrics.includes(metric.key) ?
                    <EyeIcon className="w-4 h-4" /> :
                    <EyeSlashIcon className="w-4 h-4" />
                }
                className="transition-all duration-200"
              >
                {metric.shortLabel}
              </Button>
            </HeroTooltip>
          ))}
        </div>
      </div>

      <Divider />

      {/* Chart */}
      <CardBody className="flex-1 p-6">
        {selectedMetrics.length === 0 ? (
          <div className="h-full flex items-center justify-center">
            <div className="text-center">
              <ChartBarIcon className="w-16 h-16 text-slate-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-slate-600 mb-2">No Metrics Selected</h3>
              <p className="text-sm text-slate-500 mb-4">Choose metrics above to visualize your growth data</p>
              <Button
                color="primary"
                variant="flat"
                onClick={() => setSelectedMetrics(["mrr", "customers"])}
              >
                Show Default Metrics
              </Button>
            </div>
          </div>
        ) : (
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={sampleData}
              margin={{
                top: 20,
                right: 30,
                left: 20,
                bottom: 20,
              }}
            >
              <CartesianGrid
                strokeDasharray="3 3"
                stroke="#e2e8f0"
                strokeOpacity={0.5}
              />
              <XAxis
                dataKey="month"
                stroke="#64748b"
                fontSize={12}
                tickLine={false}
                axisLine={false}
                tick={{ fill: "#64748b" }}
              />
              <YAxis
                stroke="#64748b"
                fontSize={12}
                tickLine={false}
                axisLine={false}
                tick={{ fill: "#64748b" }}
                tickFormatter={(value) => {
                  if (value >= 1000000) return `${(value / 1000000).toFixed(1)}M`;
                  if (value >= 1000) return `${(value / 1000).toFixed(0)}K`;
                  return value.toString();
                }}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: "white",
                  border: "none",
                  borderRadius: "12px",
                  boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
                  padding: "12px",
                }}
                labelStyle={{
                  color: "#1e293b",
                  fontWeight: "600",
                  marginBottom: "8px",
                  fontSize: "14px"
                }}
                formatter={(value: any, name: string) => {
                  const metric = metrics.find(m => m.key === name);
                  if (metric) {
                    return [
                      formatTooltipValue(value, metric.format),
                      metric.label
                    ];
                  }
                  return [value, name];
                }}
              />
              {showLegend && (
                <Legend
                  wrapperStyle={{
                    paddingTop: "20px",
                    fontSize: "12px"
                  }}
                  formatter={(value) => {
                    const metric = metrics.find(m => m.key === value);
                    return metric ? metric.shortLabel : value;
                  }}
                />
              )}
              {metrics.map((metric) => {
                if (selectedMetrics.includes(metric.key)) {
                  return (
                    <Line
                      key={metric.key}
                      type="monotone"
                      dataKey={metric.key}
                      stroke={metric.color}
                      strokeWidth={3}
                      dot={{
                        r: 4,
                        fill: metric.color,
                        strokeWidth: 2,
                        stroke: "white",
                        filter: "drop-shadow(0 2px 4px rgba(0,0,0,0.1))"
                      }}
                      activeDot={{
                        r: 6,
                        fill: metric.color,
                        strokeWidth: 3,
                        stroke: "white",
                        filter: "drop-shadow(0 4px 8px rgba(0,0,0,0.15))"
                      }}
                    />
                  );
                }
                return null;
              })}
            </LineChart>
          </ResponsiveContainer>
        )}
      </CardBody>
    </div>
  );
}
