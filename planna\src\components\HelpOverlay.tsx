import React, { useState } from "react";
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardBody,
  Chip,
  Progress
} from "@heroui/react";
import { 
  QuestionMarkCircleIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  XMarkIcon
} from "@heroicons/react/24/outline";

interface HelpStep {
  title: string;
  description: string;
  tip: string;
  image?: string;
}

const helpSteps: HelpStep[] = [
  {
    title: "Welcome to Planna!",
    description: "Planna helps you plan your business growth by modeling team scaling decisions and their impact on revenue, costs, and runway.",
    tip: "Start by configuring your business assumptions, then plan your team growth month by month."
  },
  {
    title: "Business Assumptions",
    description: "Set your pricing tiers, customer mix, team productivity, and salary benchmarks. These settings apply to all months in your plan.",
    tip: "Click 'Configure' to expand the assumptions panel. These are your global settings that affect all calculations."
  },
  {
    title: "Growth Visualization",
    description: "The interactive chart shows how your key metrics evolve over time. Select different metrics to visualize and track your progress.",
    tip: "Click on metric buttons to show/hide them on the chart. Hover over data points for detailed information."
  },
  {
    title: "Unit Economics",
    description: "This panel shows real-time calculations of your customer acquisition cost, lifetime value, and financial health for the selected month.",
    tip: "Watch the LTV:CAC ratio - it should be 3:1 or higher for a healthy business. Monitor your runway closely!"
  },
  {
    title: "Monthly Planning",
    description: "Plan your team growth month by month. Use the +/- buttons to adjust team sizes, and enter event budgets directly.",
    tip: "The total burn is calculated automatically based on team sizes and salaries from your business assumptions."
  },
  {
    title: "You're Ready!",
    description: "You now know the basics of Planna. Start by adjusting the business assumptions to match your company, then plan your team growth.",
    tip: "Remember: Planna auto-saves your work, and you can export your scenarios anytime using the Export button."
  }
];

interface HelpOverlayProps {
  isOpen: boolean;
  onClose: () => void;
}

export function HelpOverlay({ isOpen, onClose }: HelpOverlayProps) {
  const [currentStep, setCurrentStep] = useState(0);

  const nextStep = () => {
    if (currentStep < helpSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleClose = () => {
    setCurrentStep(0);
    onClose();
  };

  const currentHelpStep = helpSteps[currentStep];
  const progress = ((currentStep + 1) / helpSteps.length) * 100;

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={handleClose}
      size="2xl"
      backdrop="blur"
      classNames={{
        backdrop: "bg-gradient-to-t from-zinc-900 to-zinc-900/10 backdrop-opacity-20"
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              <QuestionMarkCircleIcon className="w-6 h-6 text-blue-500" />
              <span>Getting Started Guide</span>
            </div>
            <Chip size="sm" variant="flat" color="primary">
              Step {currentStep + 1} of {helpSteps.length}
            </Chip>
          </div>
          <Progress 
            value={progress} 
            color="primary" 
            className="w-full"
            size="sm"
          />
        </ModalHeader>
        
        <ModalBody>
          <Card className="border-0 shadow-sm">
            <CardBody className="p-6">
              <h3 className="text-xl font-semibold text-slate-900 mb-3">
                {currentHelpStep.title}
              </h3>
              <p className="text-slate-600 mb-4 leading-relaxed">
                {currentHelpStep.description}
              </p>
              <div className="p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
                <div className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                  <div>
                    <p className="text-sm font-medium text-blue-900 mb-1">Pro Tip:</p>
                    <p className="text-sm text-blue-800">{currentHelpStep.tip}</p>
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>
        </ModalBody>
        
        <ModalFooter>
          <div className="flex items-center justify-between w-full">
            <Button
              variant="flat"
              color="default"
              onClick={prevStep}
              isDisabled={currentStep === 0}
              startContent={<ChevronLeftIcon className="w-4 h-4" />}
            >
              Previous
            </Button>
            
            <div className="flex items-center gap-2">
              <Button
                variant="light"
                color="default"
                onClick={handleClose}
                startContent={<XMarkIcon className="w-4 h-4" />}
              >
                Skip Tour
              </Button>
              
              {currentStep === helpSteps.length - 1 ? (
                <Button
                  color="primary"
                  onClick={handleClose}
                  className="bg-gradient-to-r from-blue-500 to-purple-600"
                >
                  Get Started!
                </Button>
              ) : (
                <Button
                  color="primary"
                  onClick={nextStep}
                  endContent={<ChevronRightIcon className="w-4 h-4" />}
                >
                  Next
                </Button>
              )}
            </div>
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
