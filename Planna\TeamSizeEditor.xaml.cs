using System.Windows;
using System.Windows.Controls;

namespace Planna;

public partial class TeamSizeEditor : UserControl
{
    public static readonly DependencyProperty ValueProperty =
        DependencyProperty.Register("Value", typeof(int), typeof(TeamSizeEditor), 
            new PropertyMetadata(0, OnValueChanged));

    public int Value
    {
        get { return (int)GetValue(ValueProperty); }
        set { SetValue(ValueProperty, value); }
    }

    public static readonly DependencyProperty MinimumProperty =
        DependencyProperty.Register("Minimum", typeof(int), typeof(TeamSizeEditor), 
            new PropertyMetadata(0));

    public int Minimum
    {
        get { return (int)GetValue(MinimumProperty); }
        set { SetValue(MinimumProperty, value); }
    }

    public static readonly DependencyProperty MaximumProperty =
        DependencyProperty.Register("Maximum", typeof(int), typeof(TeamSizeEditor), 
            new PropertyMetadata(100));

    public int Maximum
    {
        get { return (int)GetValue(MaximumProperty); }
        set { SetValue(MaximumProperty, value); }
    }

    public TeamSizeEditor()
    {
        InitializeComponent();
    }

    private static void OnValueChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is TeamSizeEditor editor)
        {
            editor.ValueTextBox.Text = e.NewValue.ToString();
        }
    }

    private void IncrementButton_Click(object sender, RoutedEventArgs e)
    {
        if (Value < Maximum)
        {
            Value++;
        }
    }

    private void DecrementButton_Click(object sender, RoutedEventArgs e)
    {
        if (Value > Minimum)
        {
            Value--;
        }
    }
}
