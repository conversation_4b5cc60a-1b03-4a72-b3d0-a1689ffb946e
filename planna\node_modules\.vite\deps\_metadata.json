{"hash": "52a8d76f", "configHash": "4089ce25", "lockfileHash": "7ddd398b", "browserHash": "b854baad", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "8a0d1785", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "cb60ca8c", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "491c5375", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "08a89aa3", "needsInterop": true}, "@heroicons/react/24/outline": {"src": "../../@heroicons/react/24/outline/esm/index.js", "file": "@heroicons_react_24_outline.js", "fileHash": "88278f2c", "needsInterop": false}, "@heroui/react": {"src": "../../@heroui/react/dist/index.mjs", "file": "@heroui_react.js", "fileHash": "596babad", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "54996953", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "181fbff6", "needsInterop": true}, "recharts": {"src": "../../recharts/es6/index.js", "file": "recharts.js", "fileHash": "e4fac440", "needsInterop": false}}, "chunks": {"dist-PW4VOCVT": {"file": "dist-PW4VOCVT.js"}, "src-UW24ZMRV-5IPV6KFL": {"file": "src-UW24ZMRV-5IPV6KFL.js"}, "chunk-VVVRS5AD": {"file": "chunk-VVVRS5AD.js"}, "chunk-NYHBQTAG": {"file": "chunk-NYHBQTAG.js"}, "chunk-4QYYPGX6": {"file": "chunk-4QYYPGX6.js"}, "chunk-R3OOIZWV": {"file": "chunk-R3OOIZWV.js"}, "chunk-P5XWQMHZ": {"file": "chunk-P5XWQMHZ.js"}}}